<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

try {
    $agent = new App\Prism\PromptEngineerAgent();
    echo "Agent created successfully\n";

    $reflection = new ReflectionClass($agent);
    $property = $reflection->getProperty('systemPrompt');
    $property->setAccessible(true);
    $systemPrompt = $property->getValue($agent);

    echo "System prompt type: " . gettype($systemPrompt) . "\n";
    echo "System prompt length: " . strlen($systemPrompt) . "\n";
    echo "Contains 'specialized AI assistant': " . (str_contains($systemPrompt, 'specialized AI assistant') ? 'YES' : 'NO') . "\n";
    echo "Contains 'prompt engineering': " . (str_contains($systemPrompt, 'prompt engineering') ? 'YES' : 'NO') . "\n";

} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
