<script setup lang="ts">
import { computed } from 'vue';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { AlertCircle, X, RefreshCw } from 'lucide-vue-next';
import type { ChatError } from '@/types';

interface Props {
    error: ChatError;
}

interface Emits {
    (e: 'dismiss'): void;
    (e: 'retry'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const errorIcon = computed(() => {
    switch (props.error.type) {
        case 'validation':
            return AlertCircle;
        case 'network':
            return AlertCircle;
        case 'server':
            return AlertCircle;
        case 'stream':
            return AlertCircle;
        default:
            return AlertCircle;
    }
});

const errorTitle = computed(() => {
    switch (props.error.type) {
        case 'validation':
            return 'Invalid Input';
        case 'network':
            return 'Connection Error';
        case 'server':
            return 'Server Error';
        case 'stream':
            return 'Streaming Error';
        default:
            return 'Error';
    }
});

const errorColor = computed(() => {
    switch (props.error.type) {
        case 'validation':
            return 'text-amber-600 bg-amber-50 border-amber-200 dark:bg-amber-950 dark:border-amber-800';
        case 'network':
            return 'text-red-600 bg-red-50 border-red-200 dark:bg-red-950 dark:border-red-800';
        case 'server':
            return 'text-red-600 bg-red-50 border-red-200 dark:bg-red-950 dark:border-red-800';
        case 'stream':
            return 'text-red-600 bg-red-50 border-red-200 dark:bg-red-950 dark:border-red-800';
        default:
            return 'text-red-600 bg-red-50 border-red-200 dark:bg-red-950 dark:border-red-800';
    }
});

const canRetry = computed(() => {
    return props.error.type === 'network' || props.error.type === 'stream';
});

const handleDismiss = () => {
    emit('dismiss');
};

const handleRetry = () => {
    emit('retry');
};
</script>

<template>
    <Card :class="['border-l-4', errorColor]">
        <CardContent class="p-4">
            <div class="flex items-start gap-3">
                <component 
                    :is="errorIcon" 
                    class="h-5 w-5 flex-shrink-0 mt-0.5"
                    :class="errorColor.split(' ')[0]"
                />
                
                <div class="flex-1 min-w-0">
                    <h4 class="font-medium text-sm" :class="errorColor.split(' ')[0]">
                        {{ errorTitle }}
                    </h4>
                    
                    <p class="mt-1 text-sm text-muted-foreground">
                        {{ error.message }}
                    </p>
                    
                    <!-- Validation error details -->
                    <div v-if="error.details && Object.keys(error.details).length > 0" class="mt-2">
                        <ul class="text-sm text-muted-foreground space-y-1">
                            <li v-for="(messages, field) in error.details" :key="field">
                                <strong>{{ field }}:</strong>
                                <span v-for="(message, index) in messages" :key="index">
                                    {{ message }}{{ index < messages.length - 1 ? ', ' : '' }}
                                </span>
                            </li>
                        </ul>
                    </div>
                </div>
                
                <div class="flex items-center gap-2 flex-shrink-0">
                    <!-- Retry button -->
                    <Button
                        v-if="canRetry"
                        variant="ghost"
                        size="sm"
                        @click="handleRetry"
                        class="h-8 w-8 p-0"
                    >
                        <RefreshCw class="h-4 w-4" />
                        <span class="sr-only">Retry</span>
                    </Button>
                    
                    <!-- Dismiss button -->
                    <Button
                        variant="ghost"
                        size="sm"
                        @click="handleDismiss"
                        class="h-8 w-8 p-0"
                    >
                        <X class="h-4 w-4" />
                        <span class="sr-only">Dismiss</span>
                    </Button>
                </div>
            </div>
        </CardContent>
    </Card>
</template>
