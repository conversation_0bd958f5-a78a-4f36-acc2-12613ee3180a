<script setup lang="ts">
import { ref, computed, nextTick } from 'vue';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Send, Square } from 'lucide-vue-next';

interface Props {
    modelValue: string;
    isLoading?: boolean;
    disabled?: boolean;
    placeholder?: string;
    maxLength?: number;
}

interface Emits {
    (e: 'update:modelValue', value: string): void;
    (e: 'submit'): void;
    (e: 'stop'): void;
}

const props = withDefaults(defineProps<Props>(), {
    isLoading: false,
    disabled: false,
    placeholder: 'Ask me anything about prompt engineering...',
    maxLength: 4000,
});

const emit = defineEmits<Emits>();

const inputRef = ref<HTMLInputElement>();
const textareaRef = ref<HTMLTextAreaElement>();

const inputValue = computed({
    get: () => props.modelValue,
    set: (value: string) => emit('update:modelValue', value),
});

const canSubmit = computed(() => {
    return inputValue.value.trim().length > 0 && 
           !props.disabled && 
           !props.isLoading &&
           inputValue.value.length <= props.maxLength;
});

const characterCount = computed(() => inputValue.value.length);
const isNearLimit = computed(() => characterCount.value > props.maxLength * 0.8);
const isOverLimit = computed(() => characterCount.value > props.maxLength);

const handleSubmit = () => {
    if (canSubmit.value) {
        emit('submit');
    }
};

const handleStop = () => {
    emit('stop');
};

const handleKeydown = (event: KeyboardEvent) => {
    if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        handleSubmit();
    }
};

const focusInput = async () => {
    await nextTick();
    if (textareaRef.value) {
        textareaRef.value.focus();
    } else if (inputRef.value) {
        inputRef.value.focus();
    }
};

// Auto-resize textarea
const adjustTextareaHeight = () => {
    const textarea = textareaRef.value;
    if (textarea) {
        textarea.style.height = 'auto';
        textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
    }
};

defineExpose({
    focusInput,
});
</script>

<template>
    <div class="border-t bg-background p-4">
        <div class="mx-auto max-w-4xl">
            <div class="relative">
                <!-- Multi-line textarea for longer messages -->
                <textarea
                    ref="textareaRef"
                    v-model="inputValue"
                    :placeholder="placeholder"
                    :disabled="disabled"
                    :maxlength="maxLength"
                    class="min-h-[44px] max-h-[120px] w-full resize-none rounded-lg border border-input bg-background px-4 py-3 pr-24 text-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    rows="1"
                    @keydown="handleKeydown"
                    @input="adjustTextareaHeight"
                />
                
                <!-- Action buttons -->
                <div class="absolute bottom-2 right-2 flex items-center gap-2">
                    <!-- Character count -->
                    <div 
                        v-if="characterCount > 0"
                        class="text-xs text-muted-foreground"
                        :class="{
                            'text-amber-600': isNearLimit && !isOverLimit,
                            'text-red-600': isOverLimit,
                        }"
                    >
                        {{ characterCount }}/{{ maxLength }}
                    </div>
                    
                    <!-- Stop button (when streaming) -->
                    <Button
                        v-if="isLoading"
                        type="button"
                        size="sm"
                        variant="outline"
                        @click="handleStop"
                        class="h-8 w-8 p-0"
                    >
                        <Square class="h-4 w-4" />
                        <span class="sr-only">Stop generation</span>
                    </Button>
                    
                    <!-- Send button -->
                    <Button
                        v-else
                        type="button"
                        size="sm"
                        :disabled="!canSubmit"
                        @click="handleSubmit"
                        class="h-8 w-8 p-0"
                    >
                        <Send class="h-4 w-4" />
                        <span class="sr-only">Send message</span>
                    </Button>
                </div>
            </div>
            
            <!-- Error message for character limit -->
            <div v-if="isOverLimit" class="mt-2 text-sm text-red-600">
                Message is too long. Please keep it under {{ maxLength }} characters.
            </div>
            
            <!-- Helpful hints -->
            <div class="mt-2 text-xs text-muted-foreground">
                Press Enter to send, Shift+Enter for new line
            </div>
        </div>
    </div>
</template>
