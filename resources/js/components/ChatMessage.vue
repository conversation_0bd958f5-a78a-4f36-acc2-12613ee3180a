<script setup lang="ts">
import { computed } from 'vue';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Skeleton } from '@/components/ui/skeleton';
import type { Message } from '@ai-sdk/vue';

interface Props {
    message: Message;
    isStreaming?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    isStreaming: false,
});

const isUser = computed(() => props.message.role === 'user');
const isAssistant = computed(() => props.message.role === 'assistant');

const messageClasses = computed(() => ({
    'flex gap-3 p-4': true,
    'flex-row-reverse': isUser.value,
    'flex-row': isAssistant.value,
}));

const bubbleClasses = computed(() => ({
    'max-w-[80%] rounded-lg px-4 py-2 text-sm': true,
    'bg-blue-600 text-white': isUser.value,
    'bg-gray-100 text-gray-900 dark:bg-gray-800 dark:text-gray-100': isAssistant.value,
    'rounded-br-sm': isUser.value,
    'rounded-bl-sm': isAssistant.value,
}));

const avatarFallback = computed(() => {
    if (isUser.value) return 'U';
    return 'AI';
});
</script>

<template>
    <div :class="messageClasses">
        <Avatar class="h-8 w-8 flex-shrink-0">
            <AvatarImage 
                v-if="isUser" 
                :src="$page.props.auth?.user?.avatar" 
                :alt="$page.props.auth?.user?.name" 
            />
            <AvatarFallback 
                :class="{
                    'bg-blue-600 text-white': isUser,
                    'bg-gray-600 text-white': isAssistant,
                }"
            >
                {{ avatarFallback }}
            </AvatarFallback>
        </Avatar>
        
        <div :class="bubbleClasses">
            <div v-if="message.content" class="whitespace-pre-wrap break-words">
                {{ message.content }}
            </div>
            
            <!-- Streaming indicator -->
            <div v-if="isStreaming && !message.content" class="flex items-center gap-2">
                <div class="flex gap-1">
                    <div class="h-2 w-2 bg-current rounded-full animate-bounce [animation-delay:-0.3s]"></div>
                    <div class="h-2 w-2 bg-current rounded-full animate-bounce [animation-delay:-0.15s]"></div>
                    <div class="h-2 w-2 bg-current rounded-full animate-bounce"></div>
                </div>
                <span class="text-xs opacity-70">AI is thinking...</span>
            </div>
            
            <!-- Streaming cursor -->
            <span 
                v-if="isStreaming && message.content" 
                class="inline-block w-2 h-4 bg-current ml-1 animate-pulse"
            ></span>
        </div>
    </div>
</template>
