import type { LucideIcon } from 'lucide-vue-next';
import type { Config } from 'ziggy-js';

export interface Auth {
    user: User;
}

export interface BreadcrumbItem {
    title: string;
    href: string;
}

export interface NavItem {
    title: string;
    href: string;
    icon?: LucideIcon;
    isActive?: boolean;
}

export type AppPageProps<T extends Record<string, unknown> = Record<string, unknown>> = T & {
    name: string;
    quote: { message: string; author: string };
    auth: Auth;
    ziggy: Config & { location: string };
    sidebarOpen: boolean;
};

export interface User {
    id: number;
    name: string;
    email: string;
    avatar?: string;
    email_verified_at: string | null;
    created_at: string;
    updated_at: string;
}

export type BreadcrumbItemType = BreadcrumbItem;

// Chat Types
export interface ChatMessage {
    id: string;
    role: 'user' | 'assistant';
    content: string;
    timestamp: Date;
    isStreaming?: boolean;
}

export interface StreamChunk {
    chunk: string;
    type: 'content' | 'complete' | 'error';
    full_response?: string;
    error?: string;
    details?: Record<string, string[]>;
}

export interface UserContext {
    userId: string;
    experienceLevel: 'beginner' | 'intermediate' | 'advanced';
    preferredModels: string[];
    commonUseCases: string[];
    preferences: {
        includeExamples: boolean;
        showAdvancedTechniques: boolean;
        preferredResponseLength: 'short' | 'medium' | 'long';
        includeExplanations: boolean;
    };
    conversationHistory: Array<{
        userMessage: string;
        agentResponse: string;
        requestType: string;
        timestamp: string;
    }>;
}

export interface ChatError {
    message: string;
    type: 'validation' | 'network' | 'server' | 'stream';
    details?: Record<string, string[]>;
}
