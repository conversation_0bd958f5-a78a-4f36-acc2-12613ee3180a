<script setup lang="ts">
import { ref, onMounted, nextTick, watch, computed } from 'vue';
import { Head } from '@inertiajs/vue3';
import AppLayout from '@/layouts/AppLayout.vue';
import { useStreamingChat } from '@/composables/useStreamingChat';
import type { BreadcrumbItemType } from '@/types';

// Nuxt UI Pro components (will be available after installation)
// For now, we'll use a progressive enhancement approach

// Breadcrumbs for navigation
const breadcrumbs: BreadcrumbItemType[] = [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'Prism Chat', href: '/chat' },
];

// Chat functionality
const {
    messages,
    input,
    isLoading,
    error,
    userContext,
    hasMessages,
    isStreaming,
    sendMessage,
    clearMessages,
    loadUserContext,
    clearError,
    stop,
} = useStreamingChat();

// UI state
const messagesContainer = ref<HTMLElement>();
const showWelcome = ref(true);

// Transform messages for Nuxt UI Pro format
const chatMessages = computed(() => {
    return messages.value.map(message => ({
        id: message.id,
        content: message.content,
        role: message.role,
        createdAt: new Date(message.createdAt || Date.now()),
        avatar: message.role === 'user'
            ? { src: '/api/user/avatar', alt: 'User' }
            : { src: '/images/ai-avatar.png', alt: 'AI Assistant' }
    }));
});

// Auto-scroll to bottom when new messages arrive
const scrollToBottom = async () => {
    await nextTick();
    if (messagesContainer.value) {
        messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
    }
};

// Watch for new messages and scroll
watch(messages, scrollToBottom, { deep: true });
watch(isLoading, (loading) => {
    if (loading) {
        scrollToBottom();
    }
});

// Handle message submission
const handleSubmit = async () => {
    if (!input.value.trim() || isLoading.value) return;

    showWelcome.value = false;
    await sendMessage(input.value);
};

// Handle stopping generation
const handleStop = () => {
    stop();
};

// Handle clearing chat
const handleClearChat = () => {
    clearMessages();
    showWelcome.value = true;
    clearError();
};

// Handle error actions
const handleErrorDismiss = () => {
    clearError();
};

const handleErrorRetry = async () => {
    clearError();
    if (input.value.trim()) {
        await sendMessage(input.value);
    }
};

// Handle keyboard events
const handleKeydown = (event: KeyboardEvent) => {
    if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        handleSubmit();
    }
};

// Load user context on mount
onMounted(async () => {
    await loadUserContext();
});

// Welcome message examples
const examplePrompts = [
    "Help me create a prompt for content writing",
    "How can I improve this prompt for better results?",
    "What are the best practices for prompt engineering?",
    "Create a template for analyzing customer feedback",
];

const handleExampleClick = (prompt: string) => {
    input.value = prompt;
    handleSubmit();
};
</script>

<template>
    <Head title="Prism Chat" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="flex h-[calc(100vh-8rem)] flex-col">
            <!-- Chat Header -->
            <div class="flex items-center justify-between border-b bg-white dark:bg-gray-900 px-6 py-4">
                <div class="flex items-center gap-3">
                    <div class="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10">
                        <svg class="h-6 w-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                        </svg>
                    </div>
                    <div>
                        <h1 class="text-lg font-semibold text-gray-900 dark:text-white">Prism Prompt Engineer</h1>
                        <p class="text-sm text-gray-500 dark:text-gray-400">
                            AI-powered prompt engineering assistant
                        </p>
                    </div>
                </div>

                <div class="flex items-center gap-2">
                    <!-- Clear chat button -->
                    <button
                        v-if="hasMessages"
                        @click="handleClearChat"
                        class="inline-flex items-center gap-2 rounded-md bg-white px-3 py-2 text-sm font-medium text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 dark:bg-gray-800 dark:text-white dark:ring-gray-600 dark:hover:bg-gray-700"
                    >
                        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                        Clear Chat
                    </button>
                </div>
            </div>

            <!-- Messages area -->
            <div
                ref="messagesContainer"
                class="flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900/50"
            >
                <!-- Welcome screen -->
                <div v-if="showWelcome && !hasMessages" class="flex h-full items-center justify-center p-8">
                    <div class="w-full max-w-2xl rounded-lg bg-white p-8 shadow-sm dark:bg-gray-800">
                        <div class="text-center">
                            <div class="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-primary/10">
                                <svg class="h-8 w-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                </svg>
                            </div>
                            <h2 class="mt-4 text-2xl font-semibold text-gray-900 dark:text-white">
                                Welcome to Prism Chat
                            </h2>
                            <p class="mt-2 text-gray-600 dark:text-gray-400">
                                Your AI-powered prompt engineering assistant. Ask me anything about creating,
                                optimizing, and analyzing prompts for better AI interactions.
                            </p>
                        </div>

                        <div class="mt-8 space-y-4">
                            <h3 class="font-medium text-gray-900 dark:text-white">Try these examples:</h3>
                            <div class="grid gap-2">
                                <button
                                    v-for="prompt in examplePrompts"
                                    :key="prompt"
                                    @click="handleExampleClick(prompt)"
                                    class="rounded-lg border border-gray-200 bg-white p-3 text-left text-sm hover:bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:hover:bg-gray-700"
                                >
                                    {{ prompt }}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Chat messages -->
                <div v-else class="space-y-4 p-4">
                    <div
                        v-for="message in chatMessages"
                        :key="message.id"
                        class="flex gap-3"
                        :class="message.role === 'user' ? 'flex-row-reverse' : 'flex-row'"
                    >
                        <!-- Avatar -->
                        <div class="flex-shrink-0">
                            <div class="flex h-8 w-8 items-center justify-center rounded-full"
                                 :class="message.role === 'user'
                                    ? 'bg-primary text-primary-foreground'
                                    : 'bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-300'"
                            >
                                <span class="text-sm font-medium">
                                    {{ message.role === 'user' ? 'U' : 'AI' }}
                                </span>
                            </div>
                        </div>

                        <!-- Message bubble -->
                        <div class="max-w-[80%] rounded-lg px-4 py-2"
                             :class="message.role === 'user'
                                ? 'bg-primary text-primary-foreground'
                                : 'bg-white text-gray-900 shadow-sm dark:bg-gray-800 dark:text-gray-100'"
                        >
                            <div class="whitespace-pre-wrap break-words text-sm">
                                {{ message.content }}
                                <!-- Streaming indicator -->
                                <span
                                    v-if="isLoading && message === chatMessages[chatMessages.length - 1]"
                                    class="inline-block w-2 h-4 bg-current ml-1 animate-pulse"
                                ></span>
                            </div>
                        </div>
                    </div>

                    <!-- Typing indicator -->
                    <div v-if="isLoading && chatMessages.length === 0" class="flex gap-3">
                        <div class="flex h-8 w-8 items-center justify-center rounded-full bg-gray-200 dark:bg-gray-700">
                            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">AI</span>
                        </div>
                        <div class="rounded-lg bg-white px-4 py-2 shadow-sm dark:bg-gray-800">
                            <div class="flex items-center gap-1">
                                <div class="h-2 w-2 bg-gray-400 rounded-full animate-bounce [animation-delay:-0.3s]"></div>
                                <div class="h-2 w-2 bg-gray-400 rounded-full animate-bounce [animation-delay:-0.15s]"></div>
                                <div class="h-2 w-2 bg-gray-400 rounded-full animate-bounce"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Error display -->
            <div v-if="error" class="border-t p-4">
                <div class="mx-auto max-w-4xl">
                    <div class="rounded-lg border border-red-200 bg-red-50 p-4 dark:border-red-800 dark:bg-red-950">
                        <div class="flex items-start gap-3">
                            <svg class="h-5 w-5 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <div class="flex-1">
                                <h4 class="font-medium text-red-800 dark:text-red-200">
                                    {{ error.type === 'validation' ? 'Invalid Input' : 'Error' }}
                                </h4>
                                <p class="mt-1 text-sm text-red-700 dark:text-red-300">
                                    {{ error.message }}
                                </p>
                            </div>
                            <button
                                @click="handleErrorDismiss"
                                class="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-200"
                            >
                                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Input area -->
            <div class="border-t bg-white p-4 dark:bg-gray-900">
                <div class="mx-auto max-w-4xl">
                    <div class="relative">
                        <!-- Multi-line textarea -->
                        <textarea
                            v-model="input"
                            placeholder="Ask me anything about prompt engineering..."
                            :disabled="!!error"
                            maxlength="4000"
                            class="min-h-[44px] max-h-[120px] w-full resize-none rounded-lg border border-gray-300 bg-white px-4 py-3 pr-24 text-sm placeholder:text-gray-500 focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20 disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:placeholder:text-gray-400"
                            rows="1"
                            @keydown="handleKeydown"
                        />

                        <!-- Action buttons -->
                        <div class="absolute bottom-2 right-2 flex items-center gap-2">
                            <!-- Character count -->
                            <div
                                v-if="input.length > 0"
                                class="text-xs text-gray-500"
                                :class="{
                                    'text-amber-600': input.length > 3200,
                                    'text-red-600': input.length > 4000,
                                }"
                            >
                                {{ input.length }}/4000
                            </div>

                            <!-- Stop button (when streaming) -->
                            <button
                                v-if="isLoading"
                                @click="handleStop"
                                class="flex h-8 w-8 items-center justify-center rounded-md bg-gray-100 text-gray-600 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600"
                            >
                                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 10h6v4H9z" />
                                </svg>
                            </button>

                            <!-- Send button -->
                            <button
                                v-else
                                @click="handleSubmit"
                                :disabled="!input.trim() || !!error || input.length > 4000"
                                class="flex h-8 w-8 items-center justify-center rounded-md bg-primary text-primary-foreground hover:bg-primary/90 disabled:cursor-not-allowed disabled:opacity-50"
                            >
                                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                                </svg>
                            </button>
                        </div>
                    </div>

                    <!-- Helper text -->
                    <div class="mt-2 text-xs text-gray-500">
                        Press Enter to send, Shift+Enter for new line
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>