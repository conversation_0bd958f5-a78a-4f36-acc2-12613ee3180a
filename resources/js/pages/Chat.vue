<script setup lang="ts">
import { ref, onMounted, nextTick, watch, computed } from 'vue';
import { Head } from '@inertiajs/vue3';
import AppLayout from '@/layouts/AppLayout.vue';
import { useStreamingChat } from '@/composables/useStreamingChat';
import type { BreadcrumbItemType } from '@/types';

// Breadcrumbs for navigation
const breadcrumbs: BreadcrumbItemType[] = [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'Prism Chat', href: '/chat' },
];

// Chat functionality
const {
    messages,
    input,
    isLoading,
    error,
    userContext,
    hasMessages,
    isStreaming,
    sendMessage,
    clearMessages,
    loadUserContext,
    clearError,
    stop,
} = useStreamingChat();

// UI state
const messagesContainer = ref<HTMLElement>();
const showWelcome = ref(true);

// Transform messages for Nuxt UI Pro format
const chatMessages = computed(() => {
    return messages.value.map(message => ({
        id: message.id,
        content: message.content,
        role: message.role,
        createdAt: new Date(message.createdAt || Date.now()),
        avatar: message.role === 'user'
            ? { src: '/api/user/avatar', alt: 'User' }
            : { src: '/images/ai-avatar.png', alt: 'AI Assistant' }
    }));
});

// Auto-scroll to bottom when new messages arrive
const scrollToBottom = async () => {
    await nextTick();
    if (messagesContainer.value) {
        messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
    }
};

// Watch for new messages and scroll
watch(messages, scrollToBottom, { deep: true });
watch(isLoading, (loading) => {
    if (loading) {
        scrollToBottom();
    }
});

// Handle message submission
const handleSubmit = async () => {
    if (!input.value.trim() || isLoading.value) return;

    showWelcome.value = false;
    await sendMessage(input.value);
};

// Handle stopping generation
const handleStop = () => {
    stop();
};

// Handle clearing chat
const handleClearChat = () => {
    clearMessages();
    showWelcome.value = true;
    clearError();
};

// Handle error actions
const handleErrorDismiss = () => {
    clearError();
};

const handleErrorRetry = async () => {
    clearError();
    if (input.value.trim()) {
        await sendMessage(input.value);
    }
};

// Handle keyboard events
const handleKeydown = (event: KeyboardEvent) => {
    if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        handleSubmit();
    }
};

// Load user context on mount
onMounted(async () => {
    await loadUserContext();
});

// Welcome message examples
const examplePrompts = [
    "Help me create a prompt for content writing",
    "How can I improve this prompt for better results?",
    "What are the best practices for prompt engineering?",
    "Create a template for analyzing customer feedback",
];

const handleExampleClick = (prompt: string) => {
    input.value = prompt;
    handleSubmit();
};
</script>

<template>
    <Head title="Prism Chat" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="flex h-[calc(100vh-8rem)] flex-col">
            <!-- Chat Header -->
            <div class="flex items-center justify-between border-b bg-white dark:bg-gray-900 px-6 py-4">
                <div class="flex items-center gap-3">
                    <div class="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10">
                        <svg class="h-6 w-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                        </svg>
                    </div>
                    <div>
                        <h1 class="text-lg font-semibold text-gray-900 dark:text-white">Prism Prompt Engineer</h1>
                        <p class="text-sm text-gray-500 dark:text-gray-400">
                            AI-powered prompt engineering assistant
                        </p>
                    </div>
                </div>

                <div class="flex items-center gap-2">
                    <!-- Clear chat button -->
                    <UButton
                        v-if="hasMessages"
                        @click="handleClearChat"
                        icon="i-lucide-trash-2"
                        variant="outline"
                        color="gray"
                        size="sm"
                    >
                        Clear Chat
                    </UButton>
                </div>
            </div>

            <!-- Messages area -->
            <div
                ref="messagesContainer"
                class="flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900/50"
            >
                <!-- Welcome screen -->
                <div v-if="showWelcome && !hasMessages" class="flex h-full items-center justify-center p-8">
                    <UCard class="w-full max-w-2xl">
                        <div class="text-center">
                            <div class="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-primary/10">
                                <UIcon name="i-lucide-message-circle" class="h-8 w-8 text-primary" />
                            </div>
                            <h2 class="mt-4 text-2xl font-semibold text-gray-900 dark:text-white">
                                Welcome to Prism Chat
                            </h2>
                            <p class="mt-2 text-gray-600 dark:text-gray-400">
                                Your AI-powered prompt engineering assistant. Ask me anything about creating,
                                optimizing, and analyzing prompts for better AI interactions.
                            </p>
                        </div>

                        <div class="mt-8 space-y-4">
                            <h3 class="font-medium text-gray-900 dark:text-white">Try these examples:</h3>
                            <div class="grid gap-2">
                                <UButton
                                    v-for="prompt in examplePrompts"
                                    :key="prompt"
                                    @click="handleExampleClick(prompt)"
                                    variant="outline"
                                    color="gray"
                                    size="sm"
                                    :ui="{
                                        base: 'justify-start text-left',
                                        padding: { sm: 'px-3 py-3' }
                                    }"
                                    class="h-auto whitespace-normal"
                                >
                                    {{ prompt }}
                                </UButton>
                            </div>
                        </div>
                    </UCard>
                </div>

                <!-- Chat messages -->
                <div v-else class="space-y-4 p-4">
                    <div
                        v-for="message in chatMessages"
                        :key="message.id"
                        class="flex gap-3"
                        :class="message.role === 'user' ? 'flex-row-reverse' : 'flex-row'"
                    >
                        <!-- Avatar -->
                        <div class="flex-shrink-0">
                            <UAvatar
                                :alt="message.role === 'user' ? 'User' : 'AI Assistant'"
                                size="sm"
                                :ui="{
                                    background: message.role === 'user' ? 'bg-primary' : 'bg-gray-200 dark:bg-gray-700',
                                    text: message.role === 'user' ? 'text-primary-foreground' : 'text-gray-700 dark:text-gray-300'
                                }"
                            >
                                <span class="text-sm font-medium">
                                    {{ message.role === 'user' ? 'U' : 'AI' }}
                                </span>
                            </UAvatar>
                        </div>

                        <!-- Message bubble -->
                        <UCard
                            class="max-w-[80%]"
                            :ui="{
                                base: message.role === 'user'
                                    ? 'bg-primary text-primary-foreground'
                                    : 'bg-white dark:bg-gray-800',
                                body: { padding: 'px-4 py-2' },
                                shadow: message.role === 'user' ? '' : 'shadow-sm'
                            }"
                        >
                            <div class="whitespace-pre-wrap break-words text-sm">
                                {{ message.content }}
                                <!-- Streaming indicator -->
                                <span
                                    v-if="isLoading && message === chatMessages[chatMessages.length - 1]"
                                    class="inline-block w-2 h-4 bg-current ml-1 animate-pulse"
                                ></span>
                            </div>
                        </UCard>
                    </div>

                    <!-- Typing indicator -->
                    <div v-if="isLoading && chatMessages.length === 0" class="flex gap-3">
                        <UAvatar
                            alt="AI Assistant"
                            size="sm"
                            :ui="{
                                background: 'bg-gray-200 dark:bg-gray-700',
                                text: 'text-gray-700 dark:text-gray-300'
                            }"
                        >
                            <span class="text-sm font-medium">AI</span>
                        </UAvatar>
                        <UCard :ui="{ body: { padding: 'px-4 py-2' } }">
                            <div class="flex items-center gap-1">
                                <div class="h-2 w-2 bg-gray-400 rounded-full animate-bounce [animation-delay:-0.3s]"></div>
                                <div class="h-2 w-2 bg-gray-400 rounded-full animate-bounce [animation-delay:-0.15s]"></div>
                                <div class="h-2 w-2 bg-gray-400 rounded-full animate-bounce"></div>
                            </div>
                        </UCard>
                    </div>
                </div>
            </div>

            <!-- Error display -->
            <div v-if="error" class="border-t p-4">
                <div class="mx-auto max-w-4xl">
                    <UAlert
                        :title="error.type === 'validation' ? 'Invalid Input' : 'Error'"
                        :description="error.message"
                        icon="i-lucide-alert-circle"
                        color="red"
                        variant="soft"
                        :close-button="{ icon: 'i-lucide-x', color: 'gray', variant: 'link', onClick: handleErrorDismiss }"
                    />
                </div>
            </div>

            <!-- Input area -->
            <div class="border-t bg-white p-4 dark:bg-gray-900">
                <div class="mx-auto max-w-4xl">
                    <div class="relative">
                        <!-- Multi-line textarea using Nuxt UI -->
                        <UTextarea
                            v-model="input"
                            placeholder="Ask me anything about prompt engineering..."
                            :disabled="!!error"
                            :maxlength="4000"
                            :autoresize="true"
                            :maxrows="5"
                            :rows="1"
                            size="md"
                            variant="outline"
                            color="primary"
                            class="pr-24"
                            @keydown="handleKeydown"
                        />

                        <!-- Action buttons -->
                        <div class="absolute bottom-2 right-2 flex items-center gap-2">
                            <!-- Character count -->
                            <div
                                v-if="input.length > 0"
                                class="text-xs text-gray-500"
                                :class="{
                                    'text-amber-600': input.length > 3200,
                                    'text-red-600': input.length > 4000,
                                }"
                            >
                                {{ input.length }}/4000
                            </div>

                            <!-- Stop button (when streaming) -->
                            <UButton
                                v-if="isLoading"
                                @click="handleStop"
                                icon="i-lucide-square"
                                size="sm"
                                variant="ghost"
                                color="gray"
                                :ui="{ rounded: 'rounded-md' }"
                            />

                            <!-- Send button -->
                            <UButton
                                v-else
                                @click="handleSubmit"
                                :disabled="!input.trim() || !!error || input.length > 4000"
                                icon="i-lucide-send"
                                size="sm"
                                variant="solid"
                                color="primary"
                                :ui="{ rounded: 'rounded-md' }"
                            />
                        </div>
                    </div>

                    <!-- Helper text -->
                    <div class="mt-2 text-xs text-gray-500">
                        Press Enter to send, Shift+Enter for new line
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>