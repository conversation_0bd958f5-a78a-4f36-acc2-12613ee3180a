<?php

use App\Http\Controllers\Api\PromptEngineerController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Prism Agent Routes
|--------------------------------------------------------------------------
|
| Routes for interacting with the Prism Prompt Engineer Agent
|
*/

Route::prefix('prism')->middleware([])->group(function () {
    // Chat endpoints
    Route::post('/chat', [PromptEngineerController::class, 'chat'])
        ->name('prism.chat');

    Route::post('/chat/stream', [PromptEngineerController::class, 'chatStream'])
        ->name('prism.chat.stream');

    // Prompt analysis
    Route::post('/analyze', [PromptEngineerController::class, 'analyzePrompt'])
        ->name('prism.analyze');

    // Prompt creation
    Route::post('/create', [PromptEngineerController::class, 'createPrompt'])
        ->name('prism.create');

    // User context management
    Route::get('/context', [PromptEngineerController::class, 'getUserContextData'])
        ->name('prism.context.get');

    Route::put('/context', [PromptEngineerController::class, 'updateUserContext'])
        ->name('prism.context.update');
});

// Public endpoints (for guest users)
Route::prefix('prism/public')->group(function () {
    Route::post('/chat', [PromptEngineerController::class, 'chat'])
        ->name('prism.public.chat');

    Route::post('/analyze', [PromptEngineerController::class, 'analyzePrompt'])
        ->name('prism.public.analyze');
});
