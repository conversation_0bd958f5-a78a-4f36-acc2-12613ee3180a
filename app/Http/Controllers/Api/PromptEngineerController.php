<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Prism\PromptEngineerAgent;
use App\Prism\Models\UserContext;
use App\Prism\Models\ExperienceLevel;
use App\Prism\Models\ConversationEntry;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;

class PromptEngineerController extends Controller
{
    public function __construct(
        private readonly PromptEngineerAgent $agent
    ) {}

    /**
     * Handle a chat message with the prompt engineer agent
     */
    public function chat(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'message' => 'required|string|max:4000',
                'context' => 'sometimes|array',
                'context.experience_level' => 'sometimes|string|in:beginner,intermediate,advanced',
                'context.preferred_models' => 'sometimes|array',
                'context.preferred_models.*' => 'string',
                'context.use_cases' => 'sometimes|array',
                'context.use_cases.*' => 'string',
            ]);

            $userContext = $this->getUserContext($request);
            $response = $this->agent->handle($validated['message']);

            // Extract text from response (handle both TextResponse and string)
            $responseText = is_string($response) ? $response : $response->text;

            // Update user context with this conversation
            $conversationEntry = ConversationEntry::create(
                userMessage: $validated['message'],
                agentResponse: $responseText,
                requestType: $this->classifyRequestType($validated['message'])
            );

            $updatedContext = $userContext->withConversationEntry($conversationEntry);
            $this->saveUserContext($updatedContext);

            return response()->json([
                'success' => true,
                'response' => $responseText,
                'context' => $updatedContext->toArray(),
                'metadata' => [
                    'response_length' => strlen($responseText),
                    'request_type' => $conversationEntry->requestType,
                    'timestamp' => now()->toISOString(),
                ]
            ]);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'error' => 'Invalid input',
                'details' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            Log::error('Prompt Engineer Agent Error', [
                'message' => $e->getMessage(),
                'user_id' => Auth::id(),
                'request' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'error' => 'An error occurred while processing your request'
            ], 500);
        }
    }

    /**
     * Handle streaming chat responses
     */
    public function chatStream(Request $request): StreamedResponse
    {
        try {
            $validated = $request->validate([
                'messages' => ['required', 'array'],
                'context' => ['nullable', 'array']
            ]);

            $userContext = $this->getUserContext($request);

            return response()->stream(function () use ($validated, $userContext) {
                try {
                    $fullResponse = '';

                    foreach ($this->agent->handleStreaming($validated['messages']) as $chunk) {
                        $fullResponse .= $chunk;

                        // Send Server-Sent Events format
                        echo "data: " . json_encode([
                            'chunk' => $chunk,
                            'type' => 'content'
                        ]) . "\n\n";

                        if (ob_get_level()) {
                            ob_flush();
                        }
                        flush();
                    }

                    // Send completion event
                    echo "data: " . json_encode([
                        'type' => 'complete',
                        'full_response' => $fullResponse
                    ]) . "\n\n";

                    // Update user context
                    $conversationEntry = ConversationEntry::create(
                        userMessage: $validated['messages'],
                        agentResponse: $fullResponse,
                        requestType: $this->classifyRequestType($validated['messages'])
                    );

                    $updatedContext = $userContext->withConversationEntry($conversationEntry);
                    $this->saveUserContext($updatedContext);

                    if (ob_get_level()) {
                        ob_flush();
                    }
                    flush();
                } catch (\Exception $e) {
                    Log::error('Streaming Error', [
                        'message' => $e->getMessage(),
                        'user_id' => Auth::id(),
                        'request' => $validated
                    ]);

                    // Send error event
                    echo "data: " . json_encode([
                        'type' => 'error',
                        'error' => 'An error occurred while processing your request'
                    ]) . "\n\n";

                    if (ob_get_level()) {
                        ob_flush();
                    }
                    flush();
                }
            }, 200, [
                'Content-Type' => 'text/event-stream',
                'Cache-Control' => 'no-cache',
                'Connection' => 'keep-alive',
                'X-Accel-Buffering' => 'no', // Disable nginx buffering
                'Access-Control-Allow-Origin' => '*',
                'Access-Control-Allow-Headers' => 'Cache-Control',
            ]);
        } catch (ValidationException $e) {
            return response()->stream(function () use ($e) {
                echo "data: " . json_encode([
                    'type' => 'error',
                    'error' => 'Invalid input',
                    'details' => $e->errors()
                ]) . "\n\n";

                if (ob_get_level()) {
                    ob_flush();
                }
                flush();
            }, 422, [
                'Content-Type' => 'text/event-stream',
                'Cache-Control' => 'no-cache',
                'Connection' => 'keep-alive',
            ]);
        } catch (\Exception $e) {
            Log::error('Prompt Engineer Stream Setup Error', [
                'message' => $e->getMessage(),
                'user_id' => Auth::id(),
                'request' => $request->all()
            ]);

            return response()->stream(function () {
                echo "data: " . json_encode([
                    'type' => 'error',
                    'error' => 'An error occurred while setting up the stream'
                ]) . "\n\n";

                if (ob_get_level()) {
                    ob_flush();
                }
                flush();
            }, 500, [
                'Content-Type' => 'text/event-stream',
                'Cache-Control' => 'no-cache',
                'Connection' => 'keep-alive',
            ]);
        }
    }

    /**
     * Analyze a prompt using the agent
     */
    public function analyzePrompt(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'prompt' => 'required|string|max:8000',
                'target_model' => 'sometimes|string',
            ]);

            $userContext = $this->getUserContext($request);
            $analysisResponse = $this->agent->analyzePrompt($validated['prompt']);

            // Convert structured response to AnalysisResult
            $analysisResult = \App\Prism\Models\AnalysisResult::fromPrismResponse(
                promptId: 'temp-' . uniqid(),
                data: $analysisResponse->structured ?? []
            );

            return response()->json([
                'success' => true,
                'analysis' => $analysisResult->toArray(),
                'markdown_report' => $analysisResult->toMarkdown(),
                'metadata' => [
                    'analyzed_at' => now()->toISOString(),
                    'prompt_length' => strlen($validated['prompt']),
                    'overall_score' => $analysisResult->getOverallScore(),
                    'needs_improvement' => $analysisResult->needsImprovement(),
                ]
            ]);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'error' => 'Invalid input',
                'details' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            Log::error('Prompt Analysis Error', [
                'message' => $e->getMessage(),
                'user_id' => Auth::id(),
                'prompt_length' => strlen($request->input('prompt', ''))
            ]);

            return response()->json([
                'success' => false,
                'error' => 'An error occurred while analyzing the prompt'
            ], 500);
        }
    }

    /**
     * Create a new prompt based on requirements
     */
    public function createPrompt(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'title' => 'required|string|max:200',
                'purpose' => 'required|string|max:500',
                'target_model' => 'sometimes|string',
                'requirements' => 'required|array',
                'requirements.goal' => 'required|string',
                'requirements.context' => 'sometimes|string',
                'requirements.output_format' => 'sometimes|string',
                'requirements.constraints' => 'sometimes|array',
                'requirements.examples' => 'sometimes|array',
                'tags' => 'sometimes|array',
                'tags.*' => 'string',
            ]);

            $userContext = $this->getUserContext($request);
            $promptStructure = $this->agent->createPromptStructure($validated);

            return response()->json([
                'success' => true,
                'prompt_structure' => $promptStructure->toArray(),
                'markdown' => $promptStructure->toMarkdown(),
                'metadata' => [
                    'created_at' => now()->toISOString(),
                    'is_valid' => $promptStructure->isValid(),
                    'validation_errors' => $promptStructure->validate(),
                ]
            ]);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'error' => 'Invalid input',
                'details' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            Log::error('Prompt Creation Error', [
                'message' => $e->getMessage(),
                'user_id' => Auth::id(),
                'requirements' => $request->input('requirements', [])
            ]);

            return response()->json([
                'success' => false,
                'error' => 'An error occurred while creating the prompt'
            ], 500);
        }
    }

    /**
     * Get user context and conversation history
     */
    public function getUserContextData(Request $request): JsonResponse
    {
        $userContext = $this->getUserContext($request);

        return response()->json([
            'success' => true,
            'context' => $userContext->toArray(),
            'statistics' => [
                'conversation_count' => $userContext->getConversationCount(),
                'days_since_active' => $userContext->getDaysSinceActive(),
                'is_experienced' => $userContext->isExperienced(),
                'recent_conversations' => count($userContext->getRecentConversations()),
            ]
        ]);
    }

    /**
     * Update user context preferences
     */
    public function updateUserContext(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'experience_level' => 'sometimes|string|in:beginner,intermediate,advanced',
                'preferred_models' => 'sometimes|array',
                'preferred_models.*' => 'string',
                'use_cases' => 'sometimes|array',
                'use_cases.*' => 'string',
                'preferences' => 'sometimes|array',
                'preferences.include_examples' => 'sometimes|boolean',
                'preferences.show_advanced_techniques' => 'sometimes|boolean',
                'preferences.preferred_response_length' => 'sometimes|string|in:short,medium,long',
                'preferences.include_explanations' => 'sometimes|boolean',
            ]);

            $userContext = $this->getUserContext($request);

            // Update experience level if provided
            if (isset($validated['experience_level'])) {
                $userContext = $userContext->withExperienceLevel(
                    ExperienceLevel::from($validated['experience_level'])
                );
            }

            // Update preferred models
            if (isset($validated['preferred_models'])) {
                foreach ($validated['preferred_models'] as $model) {
                    $userContext = $userContext->withPreferredModel($model);
                }
            }

            // Update use cases
            if (isset($validated['use_cases'])) {
                foreach ($validated['use_cases'] as $useCase) {
                    $userContext = $userContext->withUseCase($useCase);
                }
            }

            // Update preferences
            if (isset($validated['preferences'])) {
                $currentPrefs = $userContext->preferences;
                $newPrefs = \App\Prism\Models\UserPreferences::fromArray(
                    array_merge($currentPrefs->toArray(), $validated['preferences'])
                );
                $userContext = $userContext->withPreferences($newPrefs);
            }

            $this->saveUserContext($userContext);

            return response()->json([
                'success' => true,
                'context' => $userContext->toArray(),
                'message' => 'User context updated successfully'
            ]);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'error' => 'Invalid input',
                'details' => $e->errors()
            ], 422);
        }
    }

    /**
     * Get or create user context
     */
    private function getUserContext(Request $request): UserContext
    {
        $userId = Auth::id() ?? 'guest-' . $request->ip();
        $cacheKey = "user_context:{$userId}";

        return Cache::remember($cacheKey, 3600, function () use ($request, $userId) {
            // Try to get context from request
            $contextData = $request->input('context', []);

            $experienceLevel = ExperienceLevel::from(
                $contextData['experience_level'] ?? 'beginner'
            );

            return UserContext::create(
                userId: $userId,
                experienceLevel: $experienceLevel,
                preferredModels: $contextData['preferred_models'] ?? [],
                commonUseCases: $contextData['use_cases'] ?? []
            );
        });
    }

    /**
     * Save user context to cache
     */
    private function saveUserContext(UserContext $context): void
    {
        $cacheKey = "user_context:{$context->userId}";
        Cache::put($cacheKey, $context, 3600);
    }

    /**
     * Classify the type of request for analytics
     */
    private function classifyRequestType(string $message): string
    {
        $messageLower = strtolower($message);

        if (str_contains($messageLower, 'create') || str_contains($messageLower, 'generate') || str_contains($messageLower, 'write')) {
            return 'creation';
        }

        if (str_contains($messageLower, 'analyze') || str_contains($messageLower, 'review') || str_contains($messageLower, 'evaluate')) {
            return 'analysis';
        }

        if (str_contains($messageLower, 'optimize') || str_contains($messageLower, 'improve') || str_contains($messageLower, 'refine')) {
            return 'optimization';
        }

        if (str_contains($messageLower, 'learn') || str_contains($messageLower, 'teach') || str_contains($messageLower, 'explain') || str_contains($messageLower, 'how')) {
            return 'education';
        }

        if (str_contains($messageLower, 'organize') || str_contains($messageLower, 'library') || str_contains($messageLower, 'template')) {
            return 'organization';
        }

        return 'general';
    }
}
