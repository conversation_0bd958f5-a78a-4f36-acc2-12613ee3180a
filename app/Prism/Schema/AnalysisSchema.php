<?php

namespace App\Prism\Schema;

use Prism\Prism\Contracts\Schema;
use Prism\Prism\Schema\ArraySchema;
use Prism\Prism\Schema\EnumSchema;
use Prism\Prism\Schema\NumberSchema;
use Prism\Prism\Schema\ObjectSchema;
use Prism\Prism\Schema\StringSchema;

class AnalysisSchema
{
    /**
     * Get the analysis schema for structured prompt analysis
     */
    public function name(): string
    {
        return 'Analytics Schema';
    }

    public static function schema(): Schema
    {
        return new ObjectSchema(
            name: 'analysis',
            description: 'Analytics Schema for prompt analysis',
            properties: [
                // Scores object with numerical ratings
                new ObjectSchema(
                    name: 'scores',
                    description: 'Numerical scores for different aspects',
                    properties: [
                        new NumberSchema('clarity', 'Clarity score (0-10): How clear and understandable the prompt is'),
                        new NumberSchema('completeness', 'Completeness score (0-10): How complete and comprehensive the prompt is'),
                        new NumberSchema('specificity', 'Specificity score (0-10): How specific and detailed the prompt is'),
                        new NumberSchema('effectiveness', 'Effectiveness score (0-10): How effective the prompt is at achieving its purpose'),
                    ],
                    requiredFields: ['clarity', 'completeness', 'specificity', 'effectiveness']
                ),

                // Array of strength strings
                new ArraySchema(
                    name: 'strengths',
                    description: 'List of identified strengths',
                    items: new StringSchema('strength', 'A single strength point')
                ),

                // Array of improvement strings
                new ArraySchema(
                    name: 'improvements',
                    description: 'List of areas for improvement',
                    items: new StringSchema('improvement', 'A single improvement suggestion')
                ),

                // Array of suggestion objects
                new ArraySchema(
                    name: 'suggestions',
                    description: 'List of detailed suggestions with categories and priorities',
                    items: new ObjectSchema(
                        name: 'suggestion',
                        description: 'A detailed suggestion with category and priority',
                        properties: [
                            new StringSchema('category', 'The category of the suggestion'),
                            new StringSchema('description', 'Detailed description of the suggestion'),
                            new EnumSchema('priority', 'Priority level of the suggestion', ['high', 'medium', 'low']),
                        ],
                        requiredFields: ['category', 'description', 'priority']
                    )
                ),
            ],
            requiredFields: ['scores', 'strengths', 'improvements', 'suggestions']
        );
    }
}
