<?php

namespace App\Prism\Models;

class UserPreferences
{
    public function __construct(
        public readonly bool $includeExamples,
        public readonly bool $showAdvancedTechniques,
        public readonly string $preferredResponseLength,
        public readonly bool $includeExplanations,
        public readonly array $favoriteCategories
    ) {}

    /**
     * Create default preferences
     */
    public static function default(): self
    {
        return new self(
            includeExamples: true,
            showAdvancedTechniques: false,
            preferredResponseLength: 'medium',
            includeExplanations: true,
            favoriteCategories: []
        );
    }

    /**
     * Convert to array
     */
    public function toArray(): array
    {
        return [
            'include_examples' => $this->includeExamples,
            'show_advanced_techniques' => $this->showAdvancedTechniques,
            'preferred_response_length' => $this->preferredResponseLength,
            'include_explanations' => $this->includeExplanations,
            'favorite_categories' => $this->favoriteCategories,
        ];
    }

    /**
     * Create from array
     */
    public static function fromArray(array $data): self
    {
        return new self(
            includeExamples: $data['include_examples'] ?? true,
            showAdvancedTechniques: $data['show_advanced_techniques'] ?? false,
            preferredResponseLength: $data['preferred_response_length'] ?? 'medium',
            includeExplanations: $data['include_explanations'] ?? true,
            favoriteCategories: $data['favorite_categories'] ?? []
        );
    }
}
