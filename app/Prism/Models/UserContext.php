<?php

namespace App\Prism\Models;

use Carbon\Carbon;

class UserContext
{
    public function __construct(
        public readonly string $userId,
        public readonly ExperienceLevel $experienceLevel,
        public readonly array $preferredModels,
        public readonly array $commonUseCases,
        public readonly array $conversationHistory,
        public readonly UserPreferences $preferences,
        public readonly Carbon $lastActive
    ) {}

    /**
     * Create a new UserContext instance
     */
    public static function create(
        string $userId,
        ExperienceLevel $experienceLevel = ExperienceLevel::BEGINNER,
        array $preferredModels = [],
        array $commonUseCases = []
    ): self {
        return new self(
            userId: $userId,
            experienceLevel: $experienceLevel,
            preferredModels: $preferredModels,
            commonUseCases: $commonUseCases,
            conversationHistory: [],
            preferences: UserPreferences::default(),
            lastActive: Carbon::now()
        );
    }

    /**
     * Convert to array representation
     */
    public function toArray(): array
    {
        return [
            'user_id' => $this->userId,
            'experience_level' => $this->experienceLevel->value,
            'preferred_models' => $this->preferredModels,
            'common_use_cases' => $this->commonUseCases,
            'conversation_history' => array_map(
                fn($entry) => $entry->toArray(),
                $this->conversationHistory
            ),
            'preferences' => $this->preferences->toArray(),
            'last_active' => $this->lastActive->toISOString(),
        ];
    }

    /**
     * Create from array representation
     */
    public static function fromArray(array $data): self
    {
        $conversationHistory = array_map(
            fn($entry) => ConversationEntry::fromArray($entry),
            $data['conversation_history'] ?? []
        );

        return new self(
            userId: $data['user_id'],
            experienceLevel: ExperienceLevel::from($data['experience_level']),
            preferredModels: $data['preferred_models'] ?? [],
            commonUseCases: $data['common_use_cases'] ?? [],
            conversationHistory: $conversationHistory,
            preferences: UserPreferences::fromArray($data['preferences'] ?? []),
            lastActive: Carbon::parse($data['last_active'])
        );
    }

    /**
     * Add a conversation entry
     */
    public function withConversationEntry(ConversationEntry $entry): self
    {
        $history = [...$this->conversationHistory, $entry];

        // Keep only the last 50 entries to prevent memory issues
        if (count($history) > 50) {
            $history = array_slice($history, -50);
        }

        return new self(
            userId: $this->userId,
            experienceLevel: $this->experienceLevel,
            preferredModels: $this->preferredModels,
            commonUseCases: $this->commonUseCases,
            conversationHistory: $history,
            preferences: $this->preferences,
            lastActive: Carbon::now()
        );
    }

    /**
     * Update experience level
     */
    public function withExperienceLevel(ExperienceLevel $level): self
    {
        return new self(
            userId: $this->userId,
            experienceLevel: $level,
            preferredModels: $this->preferredModels,
            commonUseCases: $this->commonUseCases,
            conversationHistory: $this->conversationHistory,
            preferences: $this->preferences,
            lastActive: Carbon::now()
        );
    }

    /**
     * Add a preferred model
     */
    public function withPreferredModel(string $model): self
    {
        if (in_array($model, $this->preferredModels)) {
            return $this;
        }

        return new self(
            userId: $this->userId,
            experienceLevel: $this->experienceLevel,
            preferredModels: [...$this->preferredModels, $model],
            commonUseCases: $this->commonUseCases,
            conversationHistory: $this->conversationHistory,
            preferences: $this->preferences,
            lastActive: Carbon::now()
        );
    }

    /**
     * Add a common use case
     */
    public function withUseCase(string $useCase): self
    {
        if (in_array($useCase, $this->commonUseCases)) {
            return $this;
        }

        return new self(
            userId: $this->userId,
            experienceLevel: $this->experienceLevel,
            preferredModels: $this->preferredModels,
            commonUseCases: [...$this->commonUseCases, $useCase],
            conversationHistory: $this->conversationHistory,
            preferences: $this->preferences,
            lastActive: Carbon::now()
        );
    }

    /**
     * Update preferences
     */
    public function withPreferences(UserPreferences $preferences): self
    {
        return new self(
            userId: $this->userId,
            experienceLevel: $this->experienceLevel,
            preferredModels: $this->preferredModels,
            commonUseCases: $this->commonUseCases,
            conversationHistory: $this->conversationHistory,
            preferences: $preferences,
            lastActive: Carbon::now()
        );
    }

    /**
     * Get recent conversation entries
     */
    public function getRecentConversations(int $limit = 10): array
    {
        return array_slice($this->conversationHistory, -$limit);
    }

    /**
     * Check if user is experienced
     */
    public function isExperienced(): bool
    {
        return $this->experienceLevel === ExperienceLevel::ADVANCED;
    }

    /**
     * Get conversation count
     */
    public function getConversationCount(): int
    {
        return count($this->conversationHistory);
    }

    /**
     * Get days since last active
     */
    public function getDaysSinceActive(): int
    {
        return $this->lastActive->diffInDays(Carbon::now());
    }
}
