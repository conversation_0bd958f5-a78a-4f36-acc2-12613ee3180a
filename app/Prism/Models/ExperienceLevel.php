<?php

namespace App\Prism\Models;

enum ExperienceLevel: string
{
    case BEGINNER = 'beginner';
    case INTERMEDIATE = 'intermediate';
    case ADVANCED = 'advanced';

    public function getDescription(): string
    {
        return match ($this) {
            self::BEGINNER => 'New to prompt engineering, needs guidance and examples',
            self::INTERMEDIATE => 'Some experience with prompts, can handle moderate complexity',
            self::ADVANCED => 'Experienced prompt engineer, prefers advanced techniques'
        };
    }
}
