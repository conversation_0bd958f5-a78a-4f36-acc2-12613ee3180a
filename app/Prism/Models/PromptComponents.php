<?php

namespace App\Prism\Models;

class PromptComponents
{
    public function __construct(
        public readonly ?string $context,
        public readonly ?string $role,
        public readonly string $instructions,
        public readonly ?string $outputFormat,
        public readonly array $constraints,
        public readonly array $examples
    ) {}

    /**
     * Create a new PromptComponents instance
     */
    public static function create(
        string $instructions,
        ?string $context = null,
        ?string $role = null,
        ?string $outputFormat = null,
        array $constraints = [],
        array $examples = []
    ): self {
        return new self(
            context: $context,
            role: $role,
            instructions: $instructions,
            outputFormat: $outputFormat,
            constraints: $constraints,
            examples: $examples
        );
    }

    /**
     * Convert to array representation
     */
    public function toArray(): array
    {
        return [
            'context' => $this->context,
            'role' => $this->role,
            'instructions' => $this->instructions,
            'output_format' => $this->outputFormat,
            'constraints' => $this->constraints,
            'examples' => $this->examples,
        ];
    }

    /**
     * Create from array representation
     */
    public static function fromArray(array $data): self
    {
        return new self(
            context: $data['context'] ?? null,
            role: $data['role'] ?? null,
            instructions: $data['instructions'],
            outputFormat: $data['output_format'] ?? null,
            constraints: $data['constraints'] ?? [],
            examples: $data['examples'] ?? []
        );
    }

    /**
     * Generate markdown representation
     */
    public function toMarkdown(): string
    {
        $markdown = '';

        if ($this->context) {
            $markdown .= "## Context\n\n{$this->context}\n\n";
        }

        if ($this->role) {
            $markdown .= "## Role\n\n{$this->role}\n\n";
        }

        $markdown .= "## Instructions\n\n{$this->instructions}\n\n";

        if ($this->outputFormat) {
            $markdown .= "## Output Format\n\n{$this->outputFormat}\n\n";
        }

        if (!empty($this->constraints)) {
            $markdown .= "## Constraints\n\n";
            foreach ($this->constraints as $constraint) {
                $markdown .= "- {$constraint}\n";
            }
            $markdown .= "\n";
        }

        if (!empty($this->examples)) {
            $markdown .= "## Examples\n\n";
            foreach ($this->examples as $index => $example) {
                $exampleNumber = $index + 1;
                $markdown .= "### Example {$exampleNumber}\n\n```\n{$example}\n```\n\n";
            }
        }

        return $markdown;
    }

    /**
     * Validate the components
     */
    public function validate(): array
    {
        $errors = [];

        if (empty(trim($this->instructions))) {
            $errors[] = 'Instructions are required';
        }

        // Validate constraints are strings
        foreach ($this->constraints as $index => $constraint) {
            if (!is_string($constraint)) {
                $errors[] = "Constraint at index {$index} must be a string";
            }
        }

        // Validate examples are strings
        foreach ($this->examples as $index => $example) {
            if (!is_string($example)) {
                $errors[] = "Example at index {$index} must be a string";
            }
        }

        return $errors;
    }

    /**
     * Add a constraint
     */
    public function withConstraint(string $constraint): self
    {
        return new self(
            context: $this->context,
            role: $this->role,
            instructions: $this->instructions,
            outputFormat: $this->outputFormat,
            constraints: [...$this->constraints, $constraint],
            examples: $this->examples
        );
    }

    /**
     * Add an example
     */
    public function withExample(string $example): self
    {
        return new self(
            context: $this->context,
            role: $this->role,
            instructions: $this->instructions,
            outputFormat: $this->outputFormat,
            constraints: $this->constraints,
            examples: [...$this->examples, $example]
        );
    }

    /**
     * Update instructions
     */
    public function withInstructions(string $instructions): self
    {
        return new self(
            context: $this->context,
            role: $this->role,
            instructions: $instructions,
            outputFormat: $this->outputFormat,
            constraints: $this->constraints,
            examples: $this->examples
        );
    }

    /**
     * Update context
     */
    public function withContext(?string $context): self
    {
        return new self(
            context: $context,
            role: $this->role,
            instructions: $this->instructions,
            outputFormat: $this->outputFormat,
            constraints: $this->constraints,
            examples: $this->examples
        );
    }

    /**
     * Update role
     */
    public function withRole(?string $role): self
    {
        return new self(
            context: $this->context,
            role: $role,
            instructions: $this->instructions,
            outputFormat: $this->outputFormat,
            constraints: $this->constraints,
            examples: $this->examples
        );
    }

    /**
     * Update output format
     */
    public function withOutputFormat(?string $outputFormat): self
    {
        return new self(
            context: $this->context,
            role: $this->role,
            instructions: $this->instructions,
            outputFormat: $outputFormat,
            constraints: $this->constraints,
            examples: $this->examples
        );
    }
}
