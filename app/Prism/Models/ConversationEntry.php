<?php

namespace App\Prism\Models;

use Carbon\Carbon;

class ConversationEntry
{
    public function __construct(
        public readonly string $userMessage,
        public readonly string $agentResponse,
        public readonly string $requestType,
        public readonly Carbon $timestamp
    ) {}

    /**
     * Create a new conversation entry
     */
    public static function create(
        string $userMessage,
        string $agentResponse,
        string $requestType
    ): self {
        return new self(
            userMessage: $userMessage,
            agentResponse: $agentResponse,
            requestType: $requestType,
            timestamp: Carbon::now()
        );
    }

    /**
     * Convert to array
     */
    public function toArray(): array
    {
        return [
            'user_message' => $this->userMessage,
            'agent_response' => $this->agentResponse,
            'request_type' => $this->requestType,
            'timestamp' => $this->timestamp->toISOString(),
        ];
    }

    /**
     * Create from array
     */
    public static function fromArray(array $data): self
    {
        return new self(
            userMessage: $data['user_message'],
            agentResponse: $data['agent_response'],
            requestType: $data['request_type'],
            timestamp: Carbon::parse($data['timestamp'])
        );
    }
}
