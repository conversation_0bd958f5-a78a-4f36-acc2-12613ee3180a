<?php

namespace App\Prism\Models;

use Carbon\Carbon;

class AnalysisResult
{
    public function __construct(
        public readonly string $promptId,
        public readonly AnalysisScores $scores,
        public readonly array $strengths,
        public readonly array $improvements,
        public readonly array $suggestions,
        public readonly Carbon $analyzedAt
    ) {}

    /**
     * Create a new AnalysisResult instance
     */
    public static function create(
        string $promptId,
        AnalysisScores $scores,
        array $strengths = [],
        array $improvements = [],
        array $suggestions = []
    ): self {
        return new self(
            promptId: $promptId,
            scores: $scores,
            strengths: $strengths,
            improvements: $improvements,
            suggestions: $suggestions,
            analyzedAt: Carbon::now()
        );
    }

    /**
     * Create from Prism structured response
     */
    public static function fromPrismResponse(string $promptId, array $data): self
    {
        $scores = AnalysisScores::fromArray($data['scores']);

        $suggestions = array_map(
            fn($suggestion) => OptimizationSuggestion::fromArray($suggestion),
            $data['suggestions'] ?? []
        );

        return new self(
            promptId: $promptId,
            scores: $scores,
            strengths: $data['strengths'] ?? [],
            improvements: $data['improvements'] ?? [],
            suggestions: $suggestions,
            analyzedAt: Carbon::now()
        );
    }

    /**
     * Convert to array representation
     */
    public function toArray(): array
    {
        return [
            'prompt_id' => $this->promptId,
            'scores' => $this->scores->toArray(),
            'strengths' => $this->strengths,
            'improvements' => $this->improvements,
            'suggestions' => array_map(fn($suggestion) => $suggestion->toArray(), $this->suggestions),
            'analyzed_at' => $this->analyzedAt->toISOString(),
        ];
    }

    /**
     * Create from array representation
     */
    public static function fromArray(array $data): self
    {
        $suggestions = array_map(
            fn($suggestion) => OptimizationSuggestion::fromArray($suggestion),
            $data['suggestions'] ?? []
        );

        return new self(
            promptId: $data['prompt_id'],
            scores: AnalysisScores::fromArray($data['scores']),
            strengths: $data['strengths'] ?? [],
            improvements: $data['improvements'] ?? [],
            suggestions: $suggestions,
            analyzedAt: Carbon::parse($data['analyzed_at'])
        );
    }

    /**
     * Generate markdown report of the analysis
     */
    public function toMarkdown(): string
    {
        $markdown = "# Prompt Analysis Report\n\n";
        $markdown .= "**Analyzed:** {$this->analyzedAt->format('Y-m-d H:i:s')}\n\n";

        $markdown .= "## Scores\n\n";
        $markdown .= $this->scores->toMarkdown();

        if (!empty($this->strengths)) {
            $markdown .= "\n## Strengths\n\n";
            foreach ($this->strengths as $strength) {
                $markdown .= "- {$strength}\n";
            }
        }

        if (!empty($this->improvements)) {
            $markdown .= "\n## Areas for Improvement\n\n";
            foreach ($this->improvements as $improvement) {
                $markdown .= "- {$improvement}\n";
            }
        }

        if (!empty($this->suggestions)) {
            $markdown .= "\n## Optimization Suggestions\n\n";
            foreach ($this->suggestions as $suggestion) {
                $markdown .= $suggestion->toMarkdown() . "\n";
            }
        }

        return $markdown;
    }

    /**
     * Get overall score (average of all scores)
     */
    public function getOverallScore(): float
    {
        return $this->scores->getAverage();
    }

    /**
     * Get high priority suggestions
     */
    public function getHighPrioritySuggestions(): array
    {
        return array_filter(
            $this->suggestions,
            fn($suggestion) => $suggestion->priority === 'high'
        );
    }

    /**
     * Check if the prompt needs significant improvement
     */
    public function needsImprovement(): bool
    {
        return $this->getOverallScore() < 6.0 || !empty($this->getHighPrioritySuggestions());
    }
}

class AnalysisScores
{
    public function __construct(
        public readonly float $clarity,
        public readonly float $completeness,
        public readonly float $specificity,
        public readonly float $effectiveness
    ) {}

    /**
     * Create from array
     */
    public static function fromArray(array $data): self
    {
        return new self(
            clarity: (float) $data['clarity'],
            completeness: (float) $data['completeness'],
            specificity: (float) $data['specificity'],
            effectiveness: (float) $data['effectiveness']
        );
    }

    /**
     * Convert to array
     */
    public function toArray(): array
    {
        return [
            'clarity' => $this->clarity,
            'completeness' => $this->completeness,
            'specificity' => $this->specificity,
            'effectiveness' => $this->effectiveness,
        ];
    }

    /**
     * Generate markdown representation
     */
    public function toMarkdown(): string
    {
        return <<<MARKDOWN
- **Clarity**: {$this->clarity}/10
- **Completeness**: {$this->completeness}/10
- **Specificity**: {$this->specificity}/10
- **Effectiveness**: {$this->effectiveness}/10
- **Overall**: {$this->getAverage()}/10
MARKDOWN;
    }

    /**
     * Get average score
     */
    public function getAverage(): float
    {
        return round(($this->clarity + $this->completeness + $this->specificity + $this->effectiveness) / 4, 1);
    }

    /**
     * Get lowest scoring dimension
     */
    public function getLowestDimension(): string
    {
        $scores = [
            'clarity' => $this->clarity,
            'completeness' => $this->completeness,
            'specificity' => $this->specificity,
            'effectiveness' => $this->effectiveness,
        ];

        return array_keys($scores, min($scores))[0];
    }
}

class OptimizationSuggestion
{
    public function __construct(
        public readonly string $category,
        public readonly string $description,
        public readonly string $priority,
        public readonly ?string $example = null
    ) {}

    /**
     * Create from array
     */
    public static function fromArray(array $data): self
    {
        return new self(
            category: $data['category'],
            description: $data['description'],
            priority: $data['priority'],
            example: $data['example'] ?? null
        );
    }

    /**
     * Convert to array
     */
    public function toArray(): array
    {
        return [
            'category' => $this->category,
            'description' => $this->description,
            'priority' => $this->priority,
            'example' => $this->example,
        ];
    }

    /**
     * Generate markdown representation
     */
    public function toMarkdown(): string
    {
        $priorityEmoji = match ($this->priority) {
            'high' => '🔴',
            'medium' => '🟡',
            'low' => '🟢',
            default => '⚪'
        };

        $markdown = "### {$priorityEmoji} {$this->category} ({$this->priority} priority)\n\n";
        $markdown .= "{$this->description}\n";

        if ($this->example) {
            $markdown .= "\n**Example:**\n```\n{$this->example}\n```\n";
        }

        return $markdown;
    }
}
