<?php

namespace App\Prism\Models;

use Carbon\Carbon;

class PromptMetadata
{
    public function __construct(
        public readonly Carbon $created,
        public readonly Carbon $lastModified,
        public readonly string $version,
        public readonly array $tags
    ) {}

    /**
     * Create new metadata with current timestamp
     */
    public static function create(array $tags = []): self
    {
        $now = Carbon::now();

        return new self(
            created: $now,
            lastModified: $now,
            version: '1.0.0',
            tags: $tags
        );
    }

    /**
     * Convert to array representation
     */
    public function toArray(): array
    {
        return [
            'created' => $this->created->toISOString(),
            'last_modified' => $this->lastModified->toISOString(),
            'version' => $this->version,
            'tags' => $this->tags,
        ];
    }

    /**
     * Create from array representation
     */
    public static function fromArray(array $data): self
    {
        return new self(
            created: Carbon::parse($data['created']),
            lastModified: Carbon::parse($data['last_modified']),
            version: $data['version'],
            tags: $data['tags'] ?? []
        );
    }

    /**
     * Update last modified timestamp
     */
    public function withLastModified(Carbon $lastModified): self
    {
        return new self(
            created: $this->created,
            lastModified: $lastModified,
            version: $this->incrementVersion(),
            tags: $this->tags
        );
    }

    /**
     * Add a tag
     */
    public function withTag(string $tag): self
    {
        if (in_array($tag, $this->tags)) {
            return $this;
        }

        return new self(
            created: $this->created,
            lastModified: $this->lastModified,
            version: $this->version,
            tags: [...$this->tags, $tag]
        );
    }

    /**
     * Remove a tag
     */
    public function withoutTag(string $tag): self
    {
        return new self(
            created: $this->created,
            lastModified: $this->lastModified,
            version: $this->version,
            tags: array_values(array_filter($this->tags, fn($t) => $t !== $tag))
        );
    }

    /**
     * Update tags
     */
    public function withTags(array $tags): self
    {
        return new self(
            created: $this->created,
            lastModified: $this->lastModified,
            version: $this->version,
            tags: array_unique($tags)
        );
    }

    /**
     * Increment version number
     */
    private function incrementVersion(): string
    {
        $parts = explode('.', $this->version);

        if (count($parts) !== 3) {
            return '1.0.1';
        }

        $patch = (int) $parts[2];
        $patch++;

        return "{$parts[0]}.{$parts[1]}.{$patch}";
    }

    /**
     * Check if metadata has a specific tag
     */
    public function hasTag(string $tag): bool
    {
        return in_array($tag, $this->tags);
    }

    /**
     * Get age in days
     */
    public function getAgeInDays(): int
    {
        return $this->created->diffInDays(Carbon::now());
    }

    /**
     * Get days since last modification
     */
    public function getDaysSinceModified(): int
    {
        return $this->lastModified->diffInDays(Carbon::now());
    }
}
