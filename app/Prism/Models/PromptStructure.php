<?php

namespace App\Prism\Models;

use Carbon\Carbon;
use Illuminate\Support\Str;

class PromptStructure
{
    public function __construct(
        public readonly string $id,
        public readonly string $title,
        public readonly string $purpose,
        public readonly ?string $targetModel,
        public readonly PromptComponents $components,
        public readonly PromptMetadata $metadata
    ) {}

    /**
     * Create a new PromptStructure instance
     */
    public static function create(
        string $title,
        string $purpose,
        PromptComponents $components,
        ?string $targetModel = null,
        array $tags = []
    ): self {
        return new self(
            id: Str::uuid()->toString(),
            title: $title,
            purpose: $purpose,
            targetModel: $targetModel,
            components: $components,
            metadata: PromptMetadata::create($tags)
        );
    }

    /**
     * Convert to array representation
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'purpose' => $this->purpose,
            'target_model' => $this->targetModel,
            'components' => $this->components->toArray(),
            'metadata' => $this->metadata->toArray(),
        ];
    }

    /**
     * Create from array representation
     */
    public static function fromArray(array $data): self
    {
        return new self(
            id: $data['id'],
            title: $data['title'],
            purpose: $data['purpose'],
            targetModel: $data['target_model'] ?? null,
            components: PromptComponents::fromArray($data['components']),
            metadata: PromptMetadata::fromArray($data['metadata'])
        );
    }

    /**
     * Generate markdown representation of the prompt
     */
    public function toMarkdown(): string
    {
        $markdown = "# {$this->title}\n\n";
        $markdown .= "**Purpose:** {$this->purpose}\n\n";

        if ($this->targetModel) {
            $markdown .= "**Target Model:** {$this->targetModel}\n\n";
        }

        $markdown .= $this->components->toMarkdown();

        return $markdown;
    }

    /**
     * Update the prompt with new components
     */
    public function withComponents(PromptComponents $components): self
    {
        return new self(
            id: $this->id,
            title: $this->title,
            purpose: $this->purpose,
            targetModel: $this->targetModel,
            components: $components,
            metadata: $this->metadata->withLastModified(Carbon::now())
        );
    }

    /**
     * Update metadata
     */
    public function withMetadata(PromptMetadata $metadata): self
    {
        return new self(
            id: $this->id,
            title: $this->title,
            purpose: $this->purpose,
            targetModel: $this->targetModel,
            components: $this->components,
            metadata: $metadata
        );
    }

    /**
     * Validate the prompt structure
     */
    public function validate(): array
    {
        $errors = [];

        if (empty(trim($this->title))) {
            $errors[] = 'Title is required';
        }

        if (empty(trim($this->purpose))) {
            $errors[] = 'Purpose is required';
        }

        $componentErrors = $this->components->validate();
        if (!empty($componentErrors)) {
            $errors = array_merge($errors, $componentErrors);
        }

        return $errors;
    }

    /**
     * Check if the prompt is valid
     */
    public function isValid(): bool
    {
        return empty($this->validate());
    }
}
