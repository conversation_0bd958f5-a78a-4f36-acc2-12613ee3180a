<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Prism\PromptEngineerAgent;

class PrismServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register the PromptEngineerAgent as a singleton
        $this->app->singleton(PromptEngineerAgent::class, function ($app) {
            return new PromptEngineerAgent();
        });

        // Register an alias for easier access
        $this->app->alias(PromptEngineerAgent::class, 'prompt.engineer');
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Publish Prism configuration if needed
        if ($this->app->runningInConsole()) {
            $this->publishes([
                __DIR__ . '/../../config/prism.php' => config_path('prism.php'),
            ], 'prism-config');
        }
    }

    /**
     * Get the services provided by the provider.
     */
    public function provides(): array
    {
        return [
            PromptEngineerAgent::class,
            'prompt.engineer',
        ];
    }
}
