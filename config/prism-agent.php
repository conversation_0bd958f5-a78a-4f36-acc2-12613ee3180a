<?php

use App\Prism\Schema\AnalysisSchema;

return [
    /*
    |--------------------------------------------------------------------------
    | Prism Prompt Engineer Agent Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration settings for the Prism Prompt Engineer Agent
    |
    */

    'default_provider' => env('PRISM_AGENT_PROVIDER', 'openai'),
    'default_model' => env('PRISM_AGENT_MODEL', 'gpt-4'),

    'prompt_keywords' => [
        'prompt',
        'prompting',
        'instruction',
        'context',
        'ai model',
        'gpt',
        'claude',
        'llm',
        'language model',
        'generate',
        'optimize',
        'refine',
        'improve',
        'template',
        'format',
        'structure'
    ],

    'analysis_schema' => AnalysisSchema::schema(),

    'conversation_history_limit' => 50,
    'max_response_length' => 4000,
];
