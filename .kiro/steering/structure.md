# Project Structure

## Laravel Backend Structure

### Core Application
- `app/Http/Controllers/` - Request handlers
  - `Auth/` - Authentication controllers
  - `Settings/` - User settings controllers
- `app/Http/Middleware/` - Custom middleware
- `app/Http/Requests/` - Form request validation
- `app/Models/` - Eloquent models
- `app/Providers/` - Service providers

### Configuration & Routes
- `config/` - Application configuration files
- `routes/web.php` - Web routes (includes auth.php, settings.php)
- `database/migrations/` - Database schema migrations
- `database/factories/` - Model factories for testing
- `database/seeders/` - Database seeders

## Frontend Structure

### Vue Application
- `resources/js/app.ts` - Main application entry point
- `resources/js/ssr.ts` - Server-side rendering entry
- `resources/js/pages/` - Inertia.js page components
  - `auth/` - Authentication pages
  - `settings/` - Settings pages
- `resources/js/layouts/` - Layout components
  - `AppLayout.vue` - Main app layout wrapper
  - `AuthLayout.vue` - Authentication layout
  - `app/` - App-specific layouts
  - `auth/` - Auth-specific layouts
  - `settings/` - Settings-specific layouts

### Components & Utilities
- `resources/js/components/` - Reusable Vue components
  - `ui/` - UI component library (Reka UI based)
- `resources/js/composables/` - Vue composition functions
- `resources/js/lib/utils.ts` - Utility functions (cn helper)
- `resources/js/types/` - TypeScript type definitions

### Styling
- `resources/css/app.css` - Main stylesheet with Tailwind CSS
- Uses CSS custom properties for theming
- Dark/light mode support with system preference detection

## Key Conventions

### File Naming
- **PHP**: PascalCase for classes, snake_case for files
- **Vue**: PascalCase for components, camelCase for composables
- **TypeScript**: camelCase for variables/functions, PascalCase for types/interfaces

### Component Organization
- UI components in `resources/js/components/ui/` follow atomic design
- App-specific components in `resources/js/components/`
- Each UI component has its own folder with index.ts export

### Route Structure
- Laravel routes use Inertia::render() to serve Vue components
- Page components match route structure in `resources/js/pages/`
- Breadcrumbs defined per page and passed to layouts

### State Management
- Uses Inertia.js shared data for global state
- Vue composables for reusable logic
- Props drilling for component communication

### Styling Patterns
- Tailwind CSS utility classes
- CSS custom properties for theme variables
- `cn()` utility function for conditional classes
- Component variants using class-variance-authority
