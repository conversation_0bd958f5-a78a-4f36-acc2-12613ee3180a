# Technology Stack

## Backend
- **<PERSON><PERSON> 12** - PHP web framework
- **PHP 8.2+** - Required PHP version
- **Inertia.js** - Server-side rendering adapter for SPAs
- **SQLite** - Default database (configurable)
- **Pest** - Testing framework

## Frontend
- **Vue 3** - JavaScript framework with Composition API
- **TypeScript** - Type-safe JavaScript
- **Vite** - Build tool and dev server
- **Tailwind CSS v4** - Utility-first CSS framework
- **Reka UI** - Vue component library
- **Lucide Vue Next** - Icon library

## Key Libraries
- **@inertiajs/vue3** - Inertia.js Vue 3 adapter
- **Ziggy** - Laravel route helper for JavaScript
- **@vueuse/core** - Vue composition utilities
- **class-variance-authority** - Component variant management
- **clsx & tailwind-merge** - Conditional CSS class utilities

## Development Tools
- **ESLint** - JavaScript/TypeScript linting
- **Prettier** - Code formatting
- **<PERSON><PERSON> Pint** - PHP code style fixer
- **Laravel Sail** - Docker development environment

## Common Commands

### Development
```bash
# Start development server (all services)
composer dev

# Start with SSR support
composer dev:ssr

# Frontend only
npm run dev

# Backend only
php artisan serve
```

### Building
```bash
# Build for production
npm run build

# Build with SSR
npm run build:ssr
```

### Code Quality
```bash
# Format code
npm run format
npm run lint

# PHP formatting
./vendor/bin/pint
```

### Testing
```bash
# Run tests
composer test
# or
php artisan test
```

### Database
```bash
# Run migrations
php artisan migrate

# Fresh migration with seeding
php artisan migrate:fresh --seed
```
