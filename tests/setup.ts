import { vi } from 'vitest';

// Mock Inertia.js
vi.mock('@inertiajs/vue3', () => ({
    Head: {
        name: 'Head',
        template: '<head><slot /></head>',
    },
    Link: {
        name: '<PERSON>',
        template: '<a><slot /></a>',
        props: ['href'],
    },
    router: {
        visit: vi.fn(),
        get: vi.fn(),
        post: vi.fn(),
        put: vi.fn(),
        patch: vi.fn(),
        delete: vi.fn(),
        reload: vi.fn(),
    },
    usePage: () => ({
        props: {
            auth: {
                user: {
                    id: 1,
                    name: 'Test User',
                    email: '<EMAIL>',
                    avatar: '/test-avatar.png',
                },
            },
        },
    }),
}));

// Mock AI SDK
vi.mock('@ai-sdk/vue', () => ({
    useChat: vi.fn(() => ({
        messages: { value: [] },
        input: { value: '' },
        handleSubmit: vi.fn(),
        isLoading: { value: false },
        error: { value: null },
        reload: vi.fn(),
        stop: vi.fn(),
        setMessages: vi.fn(),
    })),
}));

// Mock fetch for API calls
global.fetch = vi.fn();

// Mock CSRF token
Object.defineProperty(document, 'querySelector', {
    value: vi.fn((selector: string) => {
        if (selector === 'meta[name="csrf-token"]') {
            return {
                getAttribute: vi.fn(() => 'test-csrf-token'),
            };
        }
        return null;
    }),
    writable: true,
});

// Mock console methods to reduce noise in tests
global.console = {
    ...console,
    warn: vi.fn(),
    error: vi.fn(),
    log: vi.fn(),
};
