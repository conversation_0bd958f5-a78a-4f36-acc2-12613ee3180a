# Vue Component Tests

This directory contains Vitest tests for the Vue.js components in the Prism Prompt Engineer application.

## Test Structure

```
tests/
├── setup.ts                    # Test setup and global mocks
├── composables/
│   └── useStreamingChat.test.ts # Tests for the streaming chat composable
├── components/
│   ├── ChatMessage.test.ts      # Tests for ChatMessage component
│   ├── ChatInput.test.ts        # Tests for ChatInput component
│   └── ChatError.test.ts        # Tests for ChatError component
└── pages/
    └── Chat.test.ts             # Tests for the main Chat page
```

## Running Tests

### All Tests
```bash
npm run test
```

### Watch Mode (for development)
```bash
npm run test
```

### Run Tests Once
```bash
npm run test:run
```

### Test UI (Visual Interface)
```bash
npm run test:ui
```

### Coverage Report
```bash
npm run test:coverage
```

## Test Coverage

The tests cover:

### Composables
- **useStreamingChat**: Complete testing of the AI SDK integration, message handling, user context management, and error handling

### Components
- **ChatMessage**: Message rendering, streaming indicators, user/assistant styling, avatar handling
- **ChatInput**: Input validation, character limits, keyboard shortcuts, submit/stop functionality
- **ChatError**: Error display, retry functionality, validation error details, different error types

### Pages
- **Chat**: Full page integration, welcome screen, message flow, error handling, user interactions

## Mocking Strategy

### External Dependencies
- **@inertiajs/vue3**: Mocked for navigation and page props
- **@ai-sdk/vue**: Mocked useChat composable with controllable state
- **UI Components**: Mocked to focus on logic rather than styling
- **Fetch API**: Mocked for API calls testing

### Test Utilities
- **Vue Test Utils**: For component mounting and interaction
- **Vitest**: For test runner, assertions, and mocking
- **Happy DOM**: For lightweight DOM environment

## Best Practices

1. **Isolation**: Each test is isolated with proper setup/teardown
2. **Mocking**: External dependencies are mocked to focus on component logic
3. **Coverage**: Tests cover happy paths, edge cases, and error scenarios
4. **Readability**: Tests are well-structured with descriptive names
5. **Maintainability**: Tests are organized by feature and easy to update

## Adding New Tests

When adding new Vue components:

1. Create a test file in the appropriate directory
2. Import and mock necessary dependencies
3. Test component rendering, props, events, and user interactions
4. Include edge cases and error scenarios
5. Update this README if needed

## Debugging Tests

### Failed Tests
- Check the test output for specific assertion failures
- Use `console.log` in tests for debugging (mocked by default)
- Run single test files: `npm run test ChatMessage.test.ts`

### Coverage Issues
- Run coverage report to see uncovered lines
- Add tests for missing scenarios
- Focus on critical paths and error handling

## Integration with CI/CD

These tests are designed to run in CI/CD pipelines:
- Fast execution with mocked dependencies
- No external service dependencies
- Deterministic results
- Clear failure reporting
