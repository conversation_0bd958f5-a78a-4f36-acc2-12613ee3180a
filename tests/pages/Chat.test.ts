import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { mount } from '@vue/test-utils';
import Chat from '@/pages/Chat.vue';

// Mock the composable
const mockStreamingChat = {
    messages: { value: [] },
    input: { value: '' },
    isLoading: { value: false },
    error: { value: null },
    userContext: { value: null },
    hasMessages: { value: false },
    isStreaming: { value: false },
    sendMessage: vi.fn(),
    clearMessages: vi.fn(),
    loadUserContext: vi.fn(),
    clearError: vi.fn(),
    stop: vi.fn(),
    handleSubmit: vi.fn(),
    reload: vi.fn(),
    updateUserContext: vi.fn(),
};

vi.mock('@/composables/useStreamingChat', () => ({
    useStreamingChat: () => mockStreamingChat,
}));

// Mock AppLayout
vi.mock('@/layouts/AppLayout.vue', () => ({
    default: {
        name: 'AppLayout',
        template: '<div class="app-layout"><slot /></div>',
        props: ['breadcrumbs'],
    },
}));

describe('Chat.vue', () => {
    beforeEach(() => {
        vi.clearAllMocks();
        // Reset mock values
        mockStreamingChat.messages.value = [];
        mockStreamingChat.input.value = '';
        mockStreamingChat.isLoading.value = false;
        mockStreamingChat.error.value = null;
        mockStreamingChat.hasMessages.value = false;
        mockStreamingChat.isStreaming.value = false;
    });

    afterEach(() => {
        vi.restoreAllMocks();
    });

    it('should render correctly', () => {
        const wrapper = mount(Chat);
        
        expect(wrapper.find('.app-layout').exists()).toBe(true);
        expect(wrapper.text()).toContain('Prism Prompt Engineer');
        expect(wrapper.text()).toContain('AI-powered prompt engineering assistant');
    });

    it('should show welcome screen when no messages', () => {
        const wrapper = mount(Chat);
        
        expect(wrapper.text()).toContain('Welcome to Prism Chat');
        expect(wrapper.text()).toContain('Try these examples:');
        expect(wrapper.text()).toContain('Help me create a prompt for content writing');
    });

    it('should hide welcome screen when there are messages', async () => {
        mockStreamingChat.hasMessages.value = true;
        mockStreamingChat.messages.value = [
            { id: '1', content: 'Hello', role: 'user', createdAt: new Date() },
        ];

        const wrapper = mount(Chat);
        
        expect(wrapper.text()).not.toContain('Welcome to Prism Chat');
    });

    it('should display messages correctly', () => {
        const messages = [
            { id: '1', content: 'Hello', role: 'user', createdAt: new Date() },
            { id: '2', content: 'Hi there!', role: 'assistant', createdAt: new Date() },
        ];
        
        mockStreamingChat.hasMessages.value = true;
        mockStreamingChat.messages.value = messages;

        const wrapper = mount(Chat);
        
        expect(wrapper.text()).toContain('Hello');
        expect(wrapper.text()).toContain('Hi there!');
    });

    it('should show clear chat button when there are messages', () => {
        mockStreamingChat.hasMessages.value = true;
        
        const wrapper = mount(Chat);
        
        expect(wrapper.text()).toContain('Clear Chat');
    });

    it('should handle clear chat correctly', async () => {
        mockStreamingChat.hasMessages.value = true;
        
        const wrapper = mount(Chat);
        const clearButton = wrapper.find('button:contains("Clear Chat")');
        
        await clearButton.trigger('click');
        
        expect(mockStreamingChat.clearMessages).toHaveBeenCalled();
        expect(mockStreamingChat.clearError).toHaveBeenCalled();
    });

    it('should handle example prompt clicks', async () => {
        const wrapper = mount(Chat);
        const exampleButton = wrapper.find('button:contains("Help me create a prompt for content writing")');
        
        await exampleButton.trigger('click');
        
        expect(mockStreamingChat.input.value).toBe('Help me create a prompt for content writing');
        expect(mockStreamingChat.sendMessage).toHaveBeenCalledWith('Help me create a prompt for content writing');
    });

    it('should handle message submission', async () => {
        mockStreamingChat.input.value = 'Test message';
        
        const wrapper = mount(Chat);
        const sendButton = wrapper.find('button[type="button"]:last-child');
        
        await sendButton.trigger('click');
        
        expect(mockStreamingChat.sendMessage).toHaveBeenCalledWith('Test message');
    });

    it('should handle stop generation', async () => {
        mockStreamingChat.isLoading.value = true;
        
        const wrapper = mount(Chat);
        const stopButton = wrapper.find('button:contains("Stop")');
        
        await stopButton.trigger('click');
        
        expect(mockStreamingChat.stop).toHaveBeenCalled();
    });

    it('should show error message when error exists', () => {
        mockStreamingChat.error.value = {
            message: 'Test error message',
            type: 'network',
        };
        
        const wrapper = mount(Chat);
        
        expect(wrapper.text()).toContain('Test error message');
        expect(wrapper.text()).toContain('Error');
    });

    it('should handle error dismissal', async () => {
        mockStreamingChat.error.value = {
            message: 'Test error',
            type: 'network',
        };
        
        const wrapper = mount(Chat);
        const dismissButton = wrapper.find('button[aria-label="Dismiss error"]');
        
        await dismissButton.trigger('click');
        
        expect(mockStreamingChat.clearError).toHaveBeenCalled();
    });

    it('should disable input when there is an error', () => {
        mockStreamingChat.error.value = {
            message: 'Test error',
            type: 'validation',
        };
        
        const wrapper = mount(Chat);
        const textarea = wrapper.find('textarea');
        
        expect(textarea.attributes('disabled')).toBeDefined();
    });

    it('should show character count when typing', async () => {
        const wrapper = mount(Chat);
        const textarea = wrapper.find('textarea');
        
        mockStreamingChat.input.value = 'Test message';
        await wrapper.vm.$nextTick();
        
        expect(wrapper.text()).toContain('12/4000');
    });

    it('should show warning when approaching character limit', async () => {
        const wrapper = mount(Chat);
        
        mockStreamingChat.input.value = 'a'.repeat(3500);
        await wrapper.vm.$nextTick();
        
        const characterCount = wrapper.find('.text-amber-600');
        expect(characterCount.exists()).toBe(true);
    });

    it('should show error when exceeding character limit', async () => {
        const wrapper = mount(Chat);
        
        mockStreamingChat.input.value = 'a'.repeat(4500);
        await wrapper.vm.$nextTick();
        
        const characterCount = wrapper.find('.text-red-600');
        expect(characterCount.exists()).toBe(true);
    });

    it('should handle keyboard shortcuts correctly', async () => {
        const wrapper = mount(Chat);
        const textarea = wrapper.find('textarea');
        
        mockStreamingChat.input.value = 'Test message';
        
        // Test Enter key (should submit)
        await textarea.trigger('keydown', { key: 'Enter' });
        expect(mockStreamingChat.sendMessage).toHaveBeenCalledWith('Test message');
        
        // Test Shift+Enter (should not submit)
        vi.clearAllMocks();
        await textarea.trigger('keydown', { key: 'Enter', shiftKey: true });
        expect(mockStreamingChat.sendMessage).not.toHaveBeenCalled();
    });

    it('should load user context on mount', () => {
        mount(Chat);
        
        expect(mockStreamingChat.loadUserContext).toHaveBeenCalled();
    });

    it('should show typing indicator when loading', () => {
        mockStreamingChat.isLoading.value = true;
        mockStreamingChat.messages.value = [];
        
        const wrapper = mount(Chat);
        
        expect(wrapper.find('.animate-bounce').exists()).toBe(true);
    });

    it('should show streaming cursor on last message when loading', () => {
        mockStreamingChat.isLoading.value = true;
        mockStreamingChat.hasMessages.value = true;
        mockStreamingChat.messages.value = [
            { id: '1', content: 'Streaming message', role: 'assistant', createdAt: new Date() },
        ];
        
        const wrapper = mount(Chat);
        
        expect(wrapper.find('.animate-pulse').exists()).toBe(true);
    });
});
