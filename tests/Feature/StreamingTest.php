<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;

class StreamingTest extends TestCase
{
    use RefreshDatabase;

    private User $user;

    protected function setUp(): void
    {
        parent::setUp();
        $this->user = User::factory()->create();
        Cache::flush();
    }

    public function test_chat_stream_returns_proper_headers(): void
    {
        $response = $this->actingAs($this->user, 'sanctum')
            ->postJson('/api/prism/chat/stream', [
                'message' => 'Help me create a prompt for writing'
            ]);

        $response->assertStatus(200);
        $response->assertHeader('Content-Type', 'text/event-stream; charset=UTF-8');
        $response->assertHeader('Cache-Control', 'no-cache, private');
        $response->assertHeader('Connection', 'keep-alive');
    }

    public function test_chat_stream_handles_validation_errors(): void
    {
        $response = $this->actingAs($this->user, 'sanctum')
            ->postJson('/api/prism/chat/stream', [
                'message' => '' // Empty message should fail validation
            ]);

        $response->assertStatus(422);
        $response->assertHeader('Content-Type', 'text/event-stream; charset=UTF-8');
    }

    public function test_chat_stream_handles_long_messages(): void
    {
        $longMessage = str_repeat('This is a very long message. ', 200); // Over 4000 chars

        $response = $this->actingAs($this->user, 'sanctum')
            ->postJson('/api/prism/chat/stream', [
                'message' => $longMessage
            ]);

        $response->assertStatus(422);
        $response->assertHeader('Content-Type', 'text/event-stream; charset=UTF-8');
    }

    protected function tearDown(): void
    {
        Cache::flush();
        parent::tearDown();
    }
}
