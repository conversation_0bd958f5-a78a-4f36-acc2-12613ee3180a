<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Prism\Prism\Prism;
use Prism\Prism\Testing\TextResponseFake;
use Prism\Prism\Testing\StructuredResponseFake;

class PromptEngineerControllerTest extends TestCase
{
    use RefreshDatabase;

    private User $user;

    protected function setUp(): void
    {
        parent::setUp();
        $this->user = User::factory()->create();

        // Mock Prism responses to avoid actual API calls
        Prism::fake([
            TextResponseFake::make()->withText('This is a mocked response about prompt engineering.'),
            StructuredResponseFake::make()->withStructured([
                'scores' => [
                    'clarity' => 8.0,
                    'completeness' => 7.5,
                    'specificity' => 8.5,
                    'effectiveness' => 8.0
                ],
                'strengths' => ['Clear instructions', 'Good structure'],
                'improvements' => ['Add more context'],
                'suggestions' => [
                    [
                        'category' => 'Context',
                        'description' => 'Add background information',
                        'priority' => 'medium'
                    ]
                ]
            ])
        ]);
    }

    public function test_chat_endpoint_requires_authentication(): void
    {
        $response = $this->postJson('/api/prism/chat', [
            'message' => 'Help me create a prompt'
        ]);

        $response->assertStatus(401);
    }

    public function test_public_chat_endpoint_works_without_authentication(): void
    {
        $response = $this->postJson('/api/prism/public/chat', [
            'message' => 'Help me create a prompt for content writing'
        ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'response',
                'context',
                'metadata'
            ]);
    }

    public function test_authenticated_chat_endpoint_works(): void
    {
        $response = $this->actingAs($this->user)
            ->postJson('/api/prism/chat', [
                'message' => 'Help me optimize this prompt for GPT-4'
            ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'response',
                'context',
                'metadata' => [
                    'response_length',
                    'request_type',
                    'timestamp'
                ]
            ]);
    }

    public function test_chat_validates_message_input(): void
    {
        $response = $this->actingAs($this->user)
            ->postJson('/api/prism/chat', []);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['message']);
    }

    public function test_chat_validates_message_length(): void
    {
        $longMessage = str_repeat('a', 4001);

        $response = $this->actingAs($this->user)
            ->postJson('/api/prism/chat', [
                'message' => $longMessage
            ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['message']);
    }

    public function test_chat_accepts_context_parameters(): void
    {
        $response = $this->actingAs($this->user)
            ->postJson('/api/prism/chat', [
                'message' => 'Help me create a prompt',
                'context' => [
                    'experience_level' => 'advanced',
                    'preferred_models' => ['gpt-4', 'claude'],
                    'use_cases' => ['content creation', 'analysis']
                ]
            ]);

        $response->assertStatus(200)
            ->assertJson(['success' => true]);
    }

    public function test_analyze_prompt_endpoint_works(): void
    {
        // Mock structured response for analysis
        Prism::fake([
            StructuredResponseFake::make()->withStructured([
                'scores' => [
                    'clarity' => 8.0,
                    'completeness' => 7.5,
                    'specificity' => 8.5,
                    'effectiveness' => 8.0
                ],
                'strengths' => ['Clear instructions', 'Good structure'],
                'improvements' => ['Add more context'],
                'suggestions' => [
                    [
                        'category' => 'Context',
                        'description' => 'Add background information',
                        'priority' => 'medium'
                    ]
                ]
            ])
        ]);

        $response = $this->actingAs($this->user)
            ->postJson('/api/prism/analyze', [
                'prompt' => 'Write a blog post about AI technology trends.'
            ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'analysis' => [
                    'scores',
                    'strengths',
                    'improvements',
                    'suggestions'
                ],
                'markdown_report',
                'metadata'
            ]);
    }

    public function test_create_prompt_endpoint_works(): void
    {
        $response = $this->actingAs($this->user)
            ->postJson('/api/prism/create', [
                'title' => 'Content Writing Prompt',
                'purpose' => 'Generate blog posts about technology',
                'requirements' => [
                    'goal' => 'Create engaging blog content',
                    'context' => 'Technology blog for developers',
                    'output_format' => 'Markdown format',
                    'constraints' => ['Keep under 1000 words'],
                    'examples' => ['Example blog post structure']
                ],
                'tags' => ['content', 'blog', 'technology']
            ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'prompt_structure',
                'markdown',
                'metadata'
            ]);
    }

    public function test_get_user_context_endpoint_works(): void
    {
        $response = $this->actingAs($this->user)
            ->getJson('/api/prism/context');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'context' => [
                    'user_id',
                    'experience_level',
                    'preferred_models',
                    'common_use_cases',
                    'conversation_history',
                    'preferences',
                    'last_active'
                ],
                'statistics'
            ]);
    }

    public function test_update_user_context_endpoint_works(): void
    {
        $response = $this->actingAs($this->user)
            ->putJson('/api/prism/context', [
                'experience_level' => 'advanced',
                'preferred_models' => ['gpt-4', 'claude-3'],
                'use_cases' => ['prompt optimization', 'content creation'],
                'preferences' => [
                    'include_examples' => true,
                    'show_advanced_techniques' => true,
                    'preferred_response_length' => 'long',
                    'include_explanations' => true
                ]
            ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'User context updated successfully'
            ]);
    }

    public function test_user_context_is_cached(): void
    {
        // Make a request to create context
        $this->actingAs($this->user)
            ->postJson('/api/prism/chat', [
                'message' => 'Help me with prompts'
            ]);

        // Check that context is cached
        $cacheKey = "user_context:{$this->user->id}";
        $this->assertTrue(Cache::has($cacheKey));
    }

    public function test_request_type_classification_works(): void
    {
        $controller = app(\App\Http\Controllers\Api\PromptEngineerController::class);
        $reflection = new \ReflectionClass($controller);
        $method = $reflection->getMethod('classifyRequestType');
        $method->setAccessible(true);

        $this->assertEquals('creation', $method->invoke($controller, 'Create a prompt for me'));
        $this->assertEquals('analysis', $method->invoke($controller, 'Analyze this prompt'));
        $this->assertEquals('optimization', $method->invoke($controller, 'Optimize my prompt'));
        $this->assertEquals('education', $method->invoke($controller, 'How do I write better prompts?'));
        $this->assertEquals('organization', $method->invoke($controller, 'Help me organize my prompt library'));
        $this->assertEquals('general', $method->invoke($controller, 'Hello there'));
    }

    protected function tearDown(): void
    {
        Cache::flush();
        parent::tearDown();
    }
}
