import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { mount } from '@vue/test-utils';
import { defineComponent } from 'vue';
import { useStreamingChat } from '@/composables/useStreamingChat';

// Mock the AI SDK
const mockUseChat = vi.fn();
vi.mock('@ai-sdk/vue', () => ({
    useChat: mockUseChat,
}));

// Test component to use the composable
const TestComponent = defineComponent({
    setup() {
        return useStreamingChat();
    },
    template: '<div></div>',
});

describe('useStreamingChat', () => {
    let mockMessages: any;
    let mockInput: any;
    let mockHandleSubmit: any;
    let mockIsLoading: any;
    let mockError: any;
    let mockSetMessages: any;
    let mockStop: any;
    let mockReload: any;

    beforeEach(() => {
        // Reset mocks
        vi.clearAllMocks();
        
        // Setup mock returns
        mockMessages = { value: [] };
        mockInput = { value: '' };
        mockHandleSubmit = vi.fn();
        mockIsLoading = { value: false };
        mockError = { value: null };
        mockSetMessages = vi.fn();
        mockStop = vi.fn();
        mockReload = vi.fn();

        mockUseChat.mockReturnValue({
            messages: mockMessages,
            input: mockInput,
            handleSubmit: mockHandleSubmit,
            isLoading: mockIsLoading,
            error: mockError,
            reload: mockReload,
            stop: mockStop,
            setMessages: mockSetMessages,
        });

        // Mock fetch
        global.fetch = vi.fn();
    });

    afterEach(() => {
        vi.restoreAllMocks();
    });

    it('should initialize with correct default values', () => {
        const wrapper = mount(TestComponent);
        const vm = wrapper.vm as any;

        expect(vm.messages).toEqual([]);
        expect(vm.input).toBe('');
        expect(vm.isLoading).toBe(false);
        expect(vm.error).toBe(null);
        expect(vm.hasMessages).toBe(false);
        expect(vm.isStreaming).toBe(false);
    });

    it('should configure useChat with correct options', () => {
        mount(TestComponent);

        expect(mockUseChat).toHaveBeenCalledWith(
            expect.objectContaining({
                api: '/api/prism/chat/stream',
                headers: expect.objectContaining({
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': 'test-csrf-token',
                }),
                streamMode: 'text',
            })
        );
    });

    it('should handle sendMessage correctly', async () => {
        const wrapper = mount(TestComponent);
        const vm = wrapper.vm as any;

        mockInput.value = 'Test message';
        await vm.sendMessage('Test message');

        expect(mockInput.value).toBe('Test message');
        expect(mockHandleSubmit).toHaveBeenCalled();
    });

    it('should not send empty messages', async () => {
        const wrapper = mount(TestComponent);
        const vm = wrapper.vm as any;

        await vm.sendMessage('   ');

        expect(mockHandleSubmit).not.toHaveBeenCalled();
    });

    it('should not send messages when loading', async () => {
        mockIsLoading.value = true;
        const wrapper = mount(TestComponent);
        const vm = wrapper.vm as any;

        await vm.sendMessage('Test message');

        expect(mockHandleSubmit).not.toHaveBeenCalled();
    });

    it('should clear messages correctly', () => {
        const wrapper = mount(TestComponent);
        const vm = wrapper.vm as any;

        vm.clearMessages();

        expect(mockSetMessages).toHaveBeenCalledWith([]);
    });

    it('should load user context on request', async () => {
        const mockResponse = {
            ok: true,
            json: vi.fn().mockResolvedValue({
                success: true,
                context: { userId: '1', experienceLevel: 'intermediate' },
            }),
        };
        global.fetch = vi.fn().mockResolvedValue(mockResponse);

        const wrapper = mount(TestComponent);
        const vm = wrapper.vm as any;

        await vm.loadUserContext();

        expect(global.fetch).toHaveBeenCalledWith('/api/prism/context', {
            headers: {
                'X-CSRF-TOKEN': 'test-csrf-token',
                'X-Requested-With': 'XMLHttpRequest',
            },
        });
        expect(vm.userContext).toEqual({ userId: '1', experienceLevel: 'intermediate' });
    });

    it('should update user context', async () => {
        const mockResponse = {
            ok: true,
            json: vi.fn().mockResolvedValue({
                success: true,
                context: { userId: '1', experienceLevel: 'advanced' },
            }),
        };
        global.fetch = vi.fn().mockResolvedValue(mockResponse);

        const wrapper = mount(TestComponent);
        const vm = wrapper.vm as any;

        await vm.updateUserContext({ experienceLevel: 'advanced' });

        expect(global.fetch).toHaveBeenCalledWith('/api/prism/context', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': 'test-csrf-token',
                'X-Requested-With': 'XMLHttpRequest',
            },
            body: JSON.stringify({ experienceLevel: 'advanced' }),
        });
        expect(vm.userContext).toEqual({ userId: '1', experienceLevel: 'advanced' });
    });

    it('should handle API errors gracefully', async () => {
        global.fetch = vi.fn().mockRejectedValue(new Error('Network error'));

        const wrapper = mount(TestComponent);
        const vm = wrapper.vm as any;

        // Should not throw
        await expect(vm.loadUserContext()).resolves.toBeUndefined();
    });

    it('should compute hasMessages correctly', () => {
        const wrapper = mount(TestComponent);
        const vm = wrapper.vm as any;

        expect(vm.hasMessages).toBe(false);

        mockMessages.value = [{ id: '1', content: 'Test', role: 'user' }];
        expect(vm.hasMessages).toBe(true);
    });

    it('should compute lastMessage correctly', () => {
        const wrapper = mount(TestComponent);
        const vm = wrapper.vm as any;

        expect(vm.lastMessage).toBeUndefined();

        const messages = [
            { id: '1', content: 'First', role: 'user' },
            { id: '2', content: 'Second', role: 'assistant' },
        ];
        mockMessages.value = messages;
        expect(vm.lastMessage).toEqual(messages[1]);
    });

    it('should handle stop correctly', () => {
        const wrapper = mount(TestComponent);
        const vm = wrapper.vm as any;

        vm.stop();

        expect(mockStop).toHaveBeenCalled();
    });

    it('should handle reload correctly', () => {
        const wrapper = mount(TestComponent);
        const vm = wrapper.vm as any;

        vm.reload();

        expect(mockReload).toHaveBeenCalled();
    });
});
