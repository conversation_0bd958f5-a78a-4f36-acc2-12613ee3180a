import { describe, it, expect, vi, beforeEach } from 'vitest';
import { mount } from '@vue/test-utils';
import Chat from '@/pages/Chat.vue';

// Mock the streaming chat composable with more realistic behavior
const mockStreamingChat = {
    messages: { value: [] },
    input: { value: '' },
    isLoading: { value: false },
    error: { value: null },
    userContext: { value: null },
    hasMessages: { value: false },
    isStreaming: { value: false },
    sendMessage: vi.fn(),
    clearMessages: vi.fn(),
    loadUserContext: vi.fn(),
    clearError: vi.fn(),
    stop: vi.fn(),
    handleSubmit: vi.fn(),
    reload: vi.fn(),
    updateUserContext: vi.fn(),
};

vi.mock('@/composables/useStreamingChat', () => ({
    useStreamingChat: () => mockStreamingChat,
}));

vi.mock('@/layouts/AppLayout.vue', () => ({
    default: {
        name: 'AppLayout',
        template: '<div class="app-layout"><slot /></div>',
        props: ['breadcrumbs'],
    },
}));

describe('Chat Flow Integration', () => {
    beforeEach(() => {
        vi.clearAllMocks();
        // Reset all mock values
        mockStreamingChat.messages.value = [];
        mockStreamingChat.input.value = '';
        mockStreamingChat.isLoading.value = false;
        mockStreamingChat.error.value = null;
        mockStreamingChat.hasMessages.value = false;
        mockStreamingChat.isStreaming.value = false;
    });

    it('should handle complete chat flow from welcome to conversation', async () => {
        const wrapper = mount(Chat);

        // 1. Should start with welcome screen
        expect(wrapper.text()).toContain('Welcome to Prism Chat');
        expect(wrapper.text()).toContain('Try these examples:');

        // 2. Click on example prompt
        const exampleButton = wrapper.find('button:contains("Help me create a prompt for content writing")');
        await exampleButton.trigger('click');

        // 3. Should set input and call sendMessage
        expect(mockStreamingChat.input.value).toBe('Help me create a prompt for content writing');
        expect(mockStreamingChat.sendMessage).toHaveBeenCalledWith('Help me create a prompt for content writing');

        // 4. Simulate message being sent (update mocks)
        mockStreamingChat.hasMessages.value = true;
        mockStreamingChat.messages.value = [
            { id: '1', content: 'Help me create a prompt for content writing', role: 'user', createdAt: new Date() },
        ];
        await wrapper.vm.$nextTick();

        // 5. Welcome screen should be hidden
        expect(wrapper.text()).not.toContain('Welcome to Prism Chat');

        // 6. Should show the user message
        expect(wrapper.text()).toContain('Help me create a prompt for content writing');

        // 7. Simulate streaming response
        mockStreamingChat.isLoading.value = true;
        mockStreamingChat.messages.value.push({
            id: '2',
            content: 'I can help you create an effective prompt for content writing...',
            role: 'assistant',
            createdAt: new Date(),
        });
        await wrapper.vm.$nextTick();

        // 8. Should show streaming indicator
        expect(wrapper.find('.animate-pulse').exists()).toBe(true);

        // 9. Complete the streaming
        mockStreamingChat.isLoading.value = false;
        await wrapper.vm.$nextTick();

        // 10. Should show complete conversation
        expect(wrapper.text()).toContain('I can help you create an effective prompt for content writing...');
        expect(wrapper.find('.animate-pulse').exists()).toBe(false);
    });

    it('should handle error recovery flow', async () => {
        const wrapper = mount(Chat);

        // 1. Start with a message
        mockStreamingChat.input.value = 'Test message';

        // 2. Simulate an error
        mockStreamingChat.error.value = {
            message: 'Network connection failed',
            type: 'network',
        };
        await wrapper.vm.$nextTick();

        // 3. Should show error message
        expect(wrapper.text()).toContain('Network connection failed');
        expect(wrapper.text()).toContain('Error');

        // 4. Input should be disabled
        const textarea = wrapper.find('textarea');
        expect(textarea.attributes('disabled')).toBeDefined();

        // 5. Dismiss error
        const dismissButton = wrapper.find('button[aria-label="Dismiss error"]');
        await dismissButton.trigger('click');
        expect(mockStreamingChat.clearError).toHaveBeenCalled();

        // 6. Simulate error cleared
        mockStreamingChat.error.value = null;
        await wrapper.vm.$nextTick();

        // 7. Input should be enabled again
        expect(textarea.attributes('disabled')).toBeUndefined();
    });

    it('should handle message length validation', async () => {
        const wrapper = mount(Chat);

        // 1. Enter a very long message
        const longMessage = 'a'.repeat(4500);
        mockStreamingChat.input.value = longMessage;
        await wrapper.vm.$nextTick();

        // 2. Should show character count warning
        expect(wrapper.text()).toContain('4500/4000');
        expect(wrapper.find('.text-red-600').exists()).toBe(true);

        // 3. Send button should be disabled
        const sendButton = wrapper.find('button:last-child');
        expect(sendButton.attributes('disabled')).toBeDefined();

        // 4. Reduce message length
        const validMessage = 'a'.repeat(100);
        mockStreamingChat.input.value = validMessage;
        await wrapper.vm.$nextTick();

        // 5. Should show normal character count
        expect(wrapper.text()).toContain('100/4000');
        expect(wrapper.find('.text-red-600').exists()).toBe(false);
    });

    it('should handle stop generation flow', async () => {
        const wrapper = mount(Chat);

        // 1. Start streaming
        mockStreamingChat.isLoading.value = true;
        mockStreamingChat.hasMessages.value = true;
        mockStreamingChat.messages.value = [
            { id: '1', content: 'Partial response...', role: 'assistant', createdAt: new Date() },
        ];
        await wrapper.vm.$nextTick();

        // 2. Should show stop button
        expect(wrapper.find('.square-icon').exists()).toBe(true);

        // 3. Click stop button
        const stopButton = wrapper.find('button');
        await stopButton.trigger('click');

        // 4. Should call stop function
        expect(mockStreamingChat.stop).toHaveBeenCalled();
    });

    it('should handle clear chat flow', async () => {
        const wrapper = mount(Chat);

        // 1. Have some messages
        mockStreamingChat.hasMessages.value = true;
        mockStreamingChat.messages.value = [
            { id: '1', content: 'User message', role: 'user', createdAt: new Date() },
            { id: '2', content: 'Assistant response', role: 'assistant', createdAt: new Date() },
        ];
        await wrapper.vm.$nextTick();

        // 2. Should show clear button
        expect(wrapper.text()).toContain('Clear Chat');

        // 3. Click clear button
        const clearButton = wrapper.find('button:contains("Clear Chat")');
        await clearButton.trigger('click');

        // 4. Should call clear functions
        expect(mockStreamingChat.clearMessages).toHaveBeenCalled();
        expect(mockStreamingChat.clearError).toHaveBeenCalled();
    });

    it('should load user context on mount', () => {
        mount(Chat);

        expect(mockStreamingChat.loadUserContext).toHaveBeenCalled();
    });

    it('should handle keyboard shortcuts correctly', async () => {
        const wrapper = mount(Chat);
        const textarea = wrapper.find('textarea');

        // 1. Set input value
        mockStreamingChat.input.value = 'Test message';

        // 2. Press Enter (should submit)
        await textarea.trigger('keydown', { key: 'Enter' });
        expect(mockStreamingChat.sendMessage).toHaveBeenCalledWith('Test message');

        // 3. Press Shift+Enter (should not submit)
        vi.clearAllMocks();
        await textarea.trigger('keydown', { key: 'Enter', shiftKey: true });
        expect(mockStreamingChat.sendMessage).not.toHaveBeenCalled();
    });
});
