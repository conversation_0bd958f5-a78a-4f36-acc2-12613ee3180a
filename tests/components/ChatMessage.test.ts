import { describe, it, expect, vi, beforeEach } from 'vitest';
import { mount } from '@vue/test-utils';
import ChatMessage from '@/components/ChatMessage.vue';
import type { Message } from '@ai-sdk/vue';

// Mock UI components
vi.mock('@/components/ui/avatar', () => ({
    Avatar: { name: 'Avatar', template: '<div class="avatar"><slot /></div>' },
    AvatarFallback: { name: 'AvatarFallback', template: '<div class="avatar-fallback"><slot /></div>' },
    AvatarImage: { name: 'AvatarImage', template: '<img class="avatar-image" />', props: ['src', 'alt'] },
}));

vi.mock('@/components/ui/skeleton', () => ({
    Skeleton: { name: 'Skeleton', template: '<div class="skeleton"></div>' },
}));

describe('ChatMessage.vue', () => {
    const createMessage = (overrides: Partial<Message> = {}): Message => ({
        id: '1',
        content: 'Test message',
        role: 'user',
        createdAt: new Date(),
        ...overrides,
    });

    beforeEach(() => {
        vi.clearAllMocks();
    });

    it('should render user message correctly', () => {
        const message = createMessage({ role: 'user', content: 'Hello from user' });
        const wrapper = mount(ChatMessage, {
            props: { message },
        });

        expect(wrapper.text()).toContain('Hello from user');
        expect(wrapper.find('.flex-row-reverse').exists()).toBe(true);
        expect(wrapper.find('.bg-blue-600').exists()).toBe(true);
        expect(wrapper.text()).toContain('U');
    });

    it('should render assistant message correctly', () => {
        const message = createMessage({ role: 'assistant', content: 'Hello from AI' });
        const wrapper = mount(ChatMessage, {
            props: { message },
        });

        expect(wrapper.text()).toContain('Hello from AI');
        expect(wrapper.find('.flex-row').exists()).toBe(true);
        expect(wrapper.find('.bg-gray-100').exists()).toBe(true);
        expect(wrapper.text()).toContain('AI');
    });

    it('should show streaming indicator when streaming and no content', () => {
        const message = createMessage({ content: '' });
        const wrapper = mount(ChatMessage, {
            props: { 
                message,
                isStreaming: true,
            },
        });

        expect(wrapper.find('.animate-bounce').exists()).toBe(true);
        expect(wrapper.text()).toContain('AI is thinking...');
    });

    it('should show streaming cursor when streaming with content', () => {
        const message = createMessage({ content: 'Partial message' });
        const wrapper = mount(ChatMessage, {
            props: { 
                message,
                isStreaming: true,
            },
        });

        expect(wrapper.text()).toContain('Partial message');
        expect(wrapper.find('.animate-pulse').exists()).toBe(true);
    });

    it('should not show streaming indicators when not streaming', () => {
        const message = createMessage({ content: 'Complete message' });
        const wrapper = mount(ChatMessage, {
            props: { 
                message,
                isStreaming: false,
            },
        });

        expect(wrapper.find('.animate-bounce').exists()).toBe(false);
        expect(wrapper.find('.animate-pulse').exists()).toBe(false);
        expect(wrapper.text()).not.toContain('AI is thinking...');
    });

    it('should handle empty content gracefully', () => {
        const message = createMessage({ content: '' });
        const wrapper = mount(ChatMessage, {
            props: { message },
        });

        expect(wrapper.find('.whitespace-pre-wrap').exists()).toBe(false);
    });

    it('should preserve whitespace in message content', () => {
        const message = createMessage({ content: 'Line 1\nLine 2\n\nLine 4' });
        const wrapper = mount(ChatMessage, {
            props: { message },
        });

        const contentDiv = wrapper.find('.whitespace-pre-wrap');
        expect(contentDiv.exists()).toBe(true);
        expect(contentDiv.text()).toBe('Line 1\nLine 2\n\nLine 4');
    });

    it('should apply correct styling for user messages', () => {
        const message = createMessage({ role: 'user' });
        const wrapper = mount(ChatMessage, {
            props: { message },
        });

        const bubble = wrapper.find('.bg-blue-600');
        expect(bubble.exists()).toBe(true);
        expect(bubble.classes()).toContain('text-white');
        expect(bubble.classes()).toContain('rounded-br-sm');
    });

    it('should apply correct styling for assistant messages', () => {
        const message = createMessage({ role: 'assistant' });
        const wrapper = mount(ChatMessage, {
            props: { message },
        });

        const bubble = wrapper.find('.bg-gray-100');
        expect(bubble.exists()).toBe(true);
        expect(bubble.classes()).toContain('text-gray-900');
        expect(bubble.classes()).toContain('rounded-bl-sm');
    });

    it('should show user avatar when available', () => {
        const message = createMessage({ role: 'user' });
        const wrapper = mount(ChatMessage, {
            props: { message },
            global: {
                mocks: {
                    $page: {
                        props: {
                            auth: {
                                user: {
                                    avatar: '/user-avatar.png',
                                    name: 'John Doe',
                                },
                            },
                        },
                    },
                },
            },
        });

        const avatarImage = wrapper.find('.avatar-image');
        expect(avatarImage.exists()).toBe(true);
    });

    it('should show fallback avatar when no user avatar', () => {
        const message = createMessage({ role: 'user' });
        const wrapper = mount(ChatMessage, {
            props: { message },
            global: {
                mocks: {
                    $page: {
                        props: {
                            auth: {
                                user: {
                                    name: 'John Doe',
                                },
                            },
                        },
                    },
                },
            },
        });

        const avatarFallback = wrapper.find('.avatar-fallback');
        expect(avatarFallback.exists()).toBe(true);
        expect(avatarFallback.text()).toContain('U');
    });

    it('should handle long messages with proper word breaking', () => {
        const longMessage = 'a'.repeat(1000);
        const message = createMessage({ content: longMessage });
        const wrapper = mount(ChatMessage, {
            props: { message },
        });

        const contentDiv = wrapper.find('.break-words');
        expect(contentDiv.exists()).toBe(true);
        expect(contentDiv.text()).toBe(longMessage);
    });

    it('should limit message width to 80%', () => {
        const message = createMessage();
        const wrapper = mount(ChatMessage, {
            props: { message },
        });

        const bubble = wrapper.find('.max-w-\\[80\\%\\]');
        expect(bubble.exists()).toBe(true);
    });
});
