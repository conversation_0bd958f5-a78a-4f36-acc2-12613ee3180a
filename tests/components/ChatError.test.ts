import { describe, it, expect, vi, beforeEach } from 'vitest';
import { mount } from '@vue/test-utils';
import ChatError from '@/components/ChatError.vue';
import type { ChatError as ChatErrorType } from '@/types';

// Mock UI components
vi.mock('@/components/ui/button', () => ({
    Button: { 
        name: 'Button', 
        template: '<button><slot /></button>',
        props: ['variant', 'size'],
    },
}));

vi.mock('@/components/ui/card', () => ({
    Card: { name: 'Card', template: '<div class="card"><slot /></div>' },
    CardContent: { name: 'CardContent', template: '<div class="card-content"><slot /></div>' },
}));

vi.mock('lucide-vue-next', () => ({
    AlertCircle: { name: 'AlertCircle', template: '<svg class="alert-icon"></svg>' },
    X: { name: 'X', template: '<svg class="x-icon"></svg>' },
    RefreshCw: { name: 'RefreshCw', template: '<svg class="refresh-icon"></svg>' },
}));

describe('ChatError.vue', () => {
    beforeEach(() => {
        vi.clearAllMocks();
    });

    const createError = (overrides: Partial<ChatErrorType> = {}): ChatErrorType => ({
        message: 'Test error message',
        type: 'network',
        ...overrides,
    });

    it('should render error message correctly', () => {
        const error = createError({ message: 'Custom error message' });
        const wrapper = mount(ChatError, {
            props: { error },
        });

        expect(wrapper.text()).toContain('Custom error message');
        expect(wrapper.find('.alert-icon').exists()).toBe(true);
    });

    it('should show correct title for validation errors', () => {
        const error = createError({ type: 'validation' });
        const wrapper = mount(ChatError, {
            props: { error },
        });

        expect(wrapper.text()).toContain('Invalid Input');
    });

    it('should show correct title for network errors', () => {
        const error = createError({ type: 'network' });
        const wrapper = mount(ChatError, {
            props: { error },
        });

        expect(wrapper.text()).toContain('Connection Error');
    });

    it('should show correct title for server errors', () => {
        const error = createError({ type: 'server' });
        const wrapper = mount(ChatError, {
            props: { error },
        });

        expect(wrapper.text()).toContain('Server Error');
    });

    it('should show correct title for stream errors', () => {
        const error = createError({ type: 'stream' });
        const wrapper = mount(ChatError, {
            props: { error },
        });

        expect(wrapper.text()).toContain('Streaming Error');
    });

    it('should apply correct styling for validation errors', () => {
        const error = createError({ type: 'validation' });
        const wrapper = mount(ChatError, {
            props: { error },
        });

        expect(wrapper.find('.border-amber-200').exists()).toBe(true);
        expect(wrapper.find('.bg-amber-50').exists()).toBe(true);
    });

    it('should apply correct styling for network errors', () => {
        const error = createError({ type: 'network' });
        const wrapper = mount(ChatError, {
            props: { error },
        });

        expect(wrapper.find('.border-red-200').exists()).toBe(true);
        expect(wrapper.find('.bg-red-50').exists()).toBe(true);
    });

    it('should show retry button for retryable errors', () => {
        const error = createError({ type: 'network' });
        const wrapper = mount(ChatError, {
            props: { error },
        });

        expect(wrapper.find('.refresh-icon').exists()).toBe(true);
    });

    it('should not show retry button for validation errors', () => {
        const error = createError({ type: 'validation' });
        const wrapper = mount(ChatError, {
            props: { error },
        });

        expect(wrapper.find('.refresh-icon').exists()).toBe(false);
    });

    it('should show dismiss button for all errors', () => {
        const error = createError();
        const wrapper = mount(ChatError, {
            props: { error },
        });

        expect(wrapper.find('.x-icon').exists()).toBe(true);
    });

    it('should emit dismiss when dismiss button is clicked', async () => {
        const error = createError();
        const wrapper = mount(ChatError, {
            props: { error },
        });

        const dismissButton = wrapper.find('button:last-child');
        await dismissButton.trigger('click');

        expect(wrapper.emitted('dismiss')).toBeTruthy();
    });

    it('should emit retry when retry button is clicked', async () => {
        const error = createError({ type: 'network' });
        const wrapper = mount(ChatError, {
            props: { error },
        });

        const retryButton = wrapper.find('button:first-child');
        await retryButton.trigger('click');

        expect(wrapper.emitted('retry')).toBeTruthy();
    });

    it('should show validation error details', () => {
        const error = createError({
            type: 'validation',
            details: {
                message: ['Message is required', 'Message is too long'],
                email: ['Email is invalid'],
            },
        });
        const wrapper = mount(ChatError, {
            props: { error },
        });

        expect(wrapper.text()).toContain('message:');
        expect(wrapper.text()).toContain('Message is required, Message is too long');
        expect(wrapper.text()).toContain('email:');
        expect(wrapper.text()).toContain('Email is invalid');
    });

    it('should not show details section when no details provided', () => {
        const error = createError({ type: 'validation' });
        const wrapper = mount(ChatError, {
            props: { error },
        });

        expect(wrapper.find('ul').exists()).toBe(false);
    });

    it('should handle empty details object', () => {
        const error = createError({
            type: 'validation',
            details: {},
        });
        const wrapper = mount(ChatError, {
            props: { error },
        });

        expect(wrapper.find('ul').exists()).toBe(false);
    });

    it('should show correct icon for all error types', () => {
        const errorTypes: ChatErrorType['type'][] = ['validation', 'network', 'server', 'stream'];
        
        errorTypes.forEach(type => {
            const error = createError({ type });
            const wrapper = mount(ChatError, {
                props: { error },
            });

            expect(wrapper.find('.alert-icon').exists()).toBe(true);
        });
    });

    it('should handle long error messages gracefully', () => {
        const longMessage = 'This is a very long error message that should be displayed properly without breaking the layout or causing any issues with the component rendering.';
        const error = createError({ message: longMessage });
        const wrapper = mount(ChatError, {
            props: { error },
        });

        expect(wrapper.text()).toContain(longMessage);
        expect(wrapper.find('.flex-1').exists()).toBe(true);
    });

    it('should maintain proper layout with multiple validation errors', () => {
        const error = createError({
            type: 'validation',
            details: {
                field1: ['Error 1', 'Error 2'],
                field2: ['Error 3'],
                field3: ['Error 4', 'Error 5', 'Error 6'],
            },
        });
        const wrapper = mount(ChatError, {
            props: { error },
        });

        expect(wrapper.findAll('li')).toHaveLength(3);
        expect(wrapper.text()).toContain('field1:');
        expect(wrapper.text()).toContain('field2:');
        expect(wrapper.text()).toContain('field3:');
    });
});
