import { describe, it, expect, vi, beforeEach } from 'vitest';
import { mount } from '@vue/test-utils';
import ChatInput from '@/components/ChatInput.vue';

// Mock UI components
vi.mock('@/components/ui/button', () => ({
    Button: { 
        name: 'Button', 
        template: '<button><slot /></button>',
        props: ['variant', 'size', 'disabled', 'type'],
    },
}));

vi.mock('@/components/ui/input', () => ({
    Input: { 
        name: 'Input', 
        template: '<input />',
        props: ['modelValue', 'disabled', 'placeholder', 'maxlength'],
    },
}));

vi.mock('lucide-vue-next', () => ({
    Send: { name: 'Send', template: '<svg class="send-icon"></svg>' },
    Square: { name: 'Square', template: '<svg class="square-icon"></svg>' },
}));

describe('ChatInput.vue', () => {
    beforeEach(() => {
        vi.clearAllMocks();
    });

    it('should render correctly', () => {
        const wrapper = mount(ChatInput, {
            props: {
                modelValue: '',
            },
        });

        expect(wrapper.find('textarea').exists()).toBe(true);
        expect(wrapper.find('button').exists()).toBe(true);
    });

    it('should emit update:modelValue when input changes', async () => {
        const wrapper = mount(ChatInput, {
            props: {
                modelValue: '',
            },
        });

        const textarea = wrapper.find('textarea');
        await textarea.setValue('Test message');

        expect(wrapper.emitted('update:modelValue')).toBeTruthy();
        expect(wrapper.emitted('update:modelValue')?.[0]).toEqual(['Test message']);
    });

    it('should emit submit when send button is clicked', async () => {
        const wrapper = mount(ChatInput, {
            props: {
                modelValue: 'Test message',
            },
        });

        const sendButton = wrapper.find('button:last-child');
        await sendButton.trigger('click');

        expect(wrapper.emitted('submit')).toBeTruthy();
    });

    it('should emit stop when stop button is clicked', async () => {
        const wrapper = mount(ChatInput, {
            props: {
                modelValue: 'Test message',
                isLoading: true,
            },
        });

        const stopButton = wrapper.find('button');
        await stopButton.trigger('click');

        expect(wrapper.emitted('stop')).toBeTruthy();
    });

    it('should show stop button when loading', () => {
        const wrapper = mount(ChatInput, {
            props: {
                modelValue: 'Test message',
                isLoading: true,
            },
        });

        expect(wrapper.find('.square-icon').exists()).toBe(true);
    });

    it('should show send button when not loading', () => {
        const wrapper = mount(ChatInput, {
            props: {
                modelValue: 'Test message',
                isLoading: false,
            },
        });

        expect(wrapper.find('.send-icon').exists()).toBe(true);
    });

    it('should disable send button when input is empty', () => {
        const wrapper = mount(ChatInput, {
            props: {
                modelValue: '',
            },
        });

        const sendButton = wrapper.find('button:last-child');
        expect(sendButton.attributes('disabled')).toBeDefined();
    });

    it('should disable send button when disabled prop is true', () => {
        const wrapper = mount(ChatInput, {
            props: {
                modelValue: 'Test message',
                disabled: true,
            },
        });

        const sendButton = wrapper.find('button:last-child');
        expect(sendButton.attributes('disabled')).toBeDefined();
    });

    it('should disable send button when loading', () => {
        const wrapper = mount(ChatInput, {
            props: {
                modelValue: 'Test message',
                isLoading: true,
            },
        });

        // When loading, there should be no send button, only stop button
        expect(wrapper.find('.send-icon').exists()).toBe(false);
        expect(wrapper.find('.square-icon').exists()).toBe(true);
    });

    it('should disable send button when exceeding max length', () => {
        const longMessage = 'a'.repeat(4001);
        const wrapper = mount(ChatInput, {
            props: {
                modelValue: longMessage,
                maxLength: 4000,
            },
        });

        const sendButton = wrapper.find('button:last-child');
        expect(sendButton.attributes('disabled')).toBeDefined();
    });

    it('should show character count when typing', () => {
        const wrapper = mount(ChatInput, {
            props: {
                modelValue: 'Test message',
                maxLength: 4000,
            },
        });

        expect(wrapper.text()).toContain('12/4000');
    });

    it('should show warning color when near limit', () => {
        const nearLimitMessage = 'a'.repeat(3500);
        const wrapper = mount(ChatInput, {
            props: {
                modelValue: nearLimitMessage,
                maxLength: 4000,
            },
        });

        const characterCount = wrapper.find('.text-amber-600');
        expect(characterCount.exists()).toBe(true);
    });

    it('should show error color when over limit', () => {
        const overLimitMessage = 'a'.repeat(4500);
        const wrapper = mount(ChatInput, {
            props: {
                modelValue: overLimitMessage,
                maxLength: 4000,
            },
        });

        const characterCount = wrapper.find('.text-red-600');
        expect(characterCount.exists()).toBe(true);
    });

    it('should show error message when over limit', () => {
        const overLimitMessage = 'a'.repeat(4500);
        const wrapper = mount(ChatInput, {
            props: {
                modelValue: overLimitMessage,
                maxLength: 4000,
            },
        });

        expect(wrapper.text()).toContain('Message is too long');
        expect(wrapper.text()).toContain('keep it under 4000 characters');
    });

    it('should handle Enter key to submit', async () => {
        const wrapper = mount(ChatInput, {
            props: {
                modelValue: 'Test message',
            },
        });

        const textarea = wrapper.find('textarea');
        await textarea.trigger('keydown', { key: 'Enter' });

        expect(wrapper.emitted('submit')).toBeTruthy();
    });

    it('should not submit on Shift+Enter', async () => {
        const wrapper = mount(ChatInput, {
            props: {
                modelValue: 'Test message',
            },
        });

        const textarea = wrapper.find('textarea');
        await textarea.trigger('keydown', { key: 'Enter', shiftKey: true });

        expect(wrapper.emitted('submit')).toBeFalsy();
    });

    it('should use custom placeholder', () => {
        const customPlaceholder = 'Custom placeholder text';
        const wrapper = mount(ChatInput, {
            props: {
                modelValue: '',
                placeholder: customPlaceholder,
            },
        });

        const textarea = wrapper.find('textarea');
        expect(textarea.attributes('placeholder')).toBe(customPlaceholder);
    });

    it('should disable textarea when disabled prop is true', () => {
        const wrapper = mount(ChatInput, {
            props: {
                modelValue: '',
                disabled: true,
            },
        });

        const textarea = wrapper.find('textarea');
        expect(textarea.attributes('disabled')).toBeDefined();
    });

    it('should show helper text', () => {
        const wrapper = mount(ChatInput, {
            props: {
                modelValue: '',
            },
        });

        expect(wrapper.text()).toContain('Press Enter to send, Shift+Enter for new line');
    });

    it('should expose focusInput method', () => {
        const wrapper = mount(ChatInput, {
            props: {
                modelValue: '',
            },
        });

        expect(wrapper.vm.focusInput).toBeDefined();
        expect(typeof wrapper.vm.focusInput).toBe('function');
    });
});
