<?php

namespace Tests\Unit\Prism;

use Tests\TestCase;
use App\Prism\PromptEngineerAgent;
use App\Prism\Models\PromptStructure;
use App\Prism\Models\PromptComponents;
use App\Prism\Models\AnalysisResult;
use App\Prism\Models\UserContext;
use App\Prism\Models\ExperienceLevel;

class PromptEngineerAgentTest extends TestCase
{
    private PromptEngineerAgent $agent;

    protected function setUp(): void
    {
        parent::setUp();
        $this->agent = new PromptEngineerAgent();
    }

    public function test_agent_can_be_instantiated(): void
    {
        $this->assertInstanceOf(PromptEngineerAgent::class, $this->agent);
    }

    public function test_agent_can_be_resolved_from_container(): void
    {
        $agent = app(PromptEngineerAgent::class);
        $this->assertInstanceOf(PromptEngineerAgent::class, $agent);
    }

    public function test_agent_can_be_resolved_by_alias(): void
    {
        $agent = app('prompt.engineer');
        $this->assertInstanceOf(PromptEngineerAgent::class, $agent);
    }

    public function test_is_prompt_related_identifies_prompt_keywords(): void
    {
        $reflection = new \ReflectionClass($this->agent);
        $method = $reflection->getMethod('isPromptRelated');
        $method->setAccessible(true);

        $this->assertTrue($method->invoke($this->agent, 'Help me create a prompt for GPT-4'));
        $this->assertTrue($method->invoke($this->agent, 'How do I optimize my LLM instructions?'));
        $this->assertTrue($method->invoke($this->agent, 'Can you analyze this prompt structure?'));

        $this->assertFalse($method->invoke($this->agent, 'What is the weather today?'));
        $this->assertFalse($method->invoke($this->agent, 'Help me with my homework'));
    }

    public function test_system_prompt_is_loaded_from_blade_view(): void
    {
        $reflection = new \ReflectionClass($this->agent);
        $property = $reflection->getProperty('systemPrompt');
        $property->setAccessible(true);

        $systemPrompt = $property->getValue($this->agent);

        $this->assertIsString($systemPrompt);
        $this->assertStringContainsString('specialized AI assistant', $systemPrompt);
        $this->assertStringContainsString('prompt engineering', $systemPrompt);
    }

    public function test_decline_non_prompt_request_uses_blade_view(): void
    {
        $reflection = new \ReflectionClass($this->agent);
        $method = $reflection->getMethod('declineNonPromptRequest');
        $method->setAccessible(true);

        $response = $method->invoke($this->agent);

        $this->assertStringContainsString('Focus on Prompt Engineering', $response);
        $this->assertStringContainsString('specialized prompt engineering assistant', $response);
    }

    public function test_extract_section_parses_markdown_correctly(): void
    {
        $reflection = new \ReflectionClass($this->agent);
        $method = $reflection->getMethod('extractSection');
        $method->setAccessible(true);

        $markdown = <<<'MARKDOWN'
# Title

## Context

This is the context section.

## Instructions

These are the instructions.

## Output Format

This is the output format.
MARKDOWN;

        $context = $method->invoke($this->agent, $markdown, 'Context');
        $instructions = $method->invoke($this->agent, $markdown, 'Instructions');
        $outputFormat = $method->invoke($this->agent, $markdown, 'Output Format');

        $this->assertEquals('This is the context section.', $context);
        $this->assertEquals('These are the instructions.', $instructions);
        $this->assertEquals('This is the output format.', $outputFormat);
    }

    public function test_extract_list_items_parses_markdown_lists(): void
    {
        $reflection = new \ReflectionClass($this->agent);
        $method = $reflection->getMethod('extractListItems');
        $method->setAccessible(true);

        $markdown = <<<'MARKDOWN'
## Constraints

- Keep responses under 100 words
- Use formal tone
- Include examples
MARKDOWN;

        $items = $method->invoke($this->agent, $markdown, 'Constraints');

        $this->assertCount(3, $items);
        $this->assertEquals('Keep responses under 100 words', $items[0]);
        $this->assertEquals('Use formal tone', $items[1]);
        $this->assertEquals('Include examples', $items[2]);
    }

    public function test_extract_code_blocks_finds_code_examples(): void
    {
        $reflection = new \ReflectionClass($this->agent);
        $method = $reflection->getMethod('extractCodeBlocks');
        $method->setAccessible(true);

        $markdown = <<<'MARKDOWN'
Here's an example:

```
Example 1 content
```

And another:

```python
print("Hello World")
```
MARKDOWN;

        $codeBlocks = $method->invoke($this->agent, $markdown);

        $this->assertCount(2, $codeBlocks);
        $this->assertEquals('Example 1 content', $codeBlocks[0]);
        $this->assertEquals('print("Hello World")', $codeBlocks[1]);
    }
}
